# 🌍 衡阳GPS实时追踪系统

基于STM32 + ESP-01 + ThingSpeak + OpenStreetMap的实时GPS追踪前端系统

## 📋 功能特点

### 🗺️ 地图功能
- **OpenStreetMap显示** - 免费开源地图
- **卫星图切换** - 支持多种地图图层
- **实时位置标记** - 显示设备当前位置
- **历史轨迹显示** - 可视化移动路径
- **自动居中跟踪** - 自动跟随设备移动
- **全屏地图模式** - 支持全屏查看

### 📡 数据功能
- **ThingSpeak集成** - 云端数据存储
- **实时数据更新** - 可配置更新间隔
- **历史数据查看** - 显示历史轨迹
- **跨网络访问** - 任何网络都能访问
- **模拟数据模式** - 离线测试功能

### 🎛️ 控制功能
- **手动刷新** - 立即获取最新数据
- **轨迹清除** - 清空历史轨迹
- **设置调整** - 自定义更新间隔
- **状态监控** - 实时连接状态显示

## 🚀 快速开始

### 1. ThingSpeak配置

1. **注册ThingSpeak账号**
   - 访问: https://thingspeak.com/
   - 创建免费账号

2. **创建Channel**
   - 点击 "Channels" → "New Channel"
   - 设置字段:
     - Field 1: Latitude (纬度)
     - Field 2: Longitude (经度)
     - Field 3: Altitude (海拔)
   - 保存并获取Channel ID和API Keys

3. **配置API密钥**
   - 编辑 `js/config.js` 文件
   - 替换以下配置:
   ```javascript
   THINGSPEAK: {
       CHANNEL_ID: 'YOUR_CHANNEL_ID',        // 您的频道ID
       READ_API_KEY: 'YOUR_READ_API_KEY',    // 读取API密钥
       WRITE_API_KEY: 'YOUR_WRITE_API_KEY'   // 写入API密钥(可选)
   }
   ```

### 2. STM32端配置

确保您的STM32代码中ThingSpeak URL格式正确:
```c
// 发送数据到ThingSpeak
sprintf(url, "/update?api_key=YOUR_WRITE_API_KEY&field1=%.6f&field2=%.6f&field3=%.1f", 
        latitude, longitude, altitude);
```

### 3. 部署前端

#### 方法1: 本地运行
```bash
# 直接用浏览器打开
open index.html
```

#### 方法2: 本地服务器
```bash
# Python 3
python -m http.server 8000

# Node.js (需要安装http-server)
npx http-server

# 然后访问: http://localhost:8000
```

#### 方法3: 云端部署
- **GitHub Pages** (免费)
- **Netlify** (免费)
- **Vercel** (免费)
- **自己的服务器**

## 📁 文件结构

```
GPS_Tracker_Frontend/
├── index.html              # 主页面
├── css/
│   └── style.css          # 样式文件
├── js/
│   ├── config.js          # 配置文件
│   ├── thingspeak.js      # ThingSpeak API
│   ├── map.js             # 地图管理
│   └── app.js             # 主应用逻辑
└── README.md              # 说明文档
```

## ⚙️ 配置说明

### 地图配置
```javascript
MAP: {
    DEFAULT_CENTER: [26.8968, 112.5707],  // 衡阳坐标
    DEFAULT_ZOOM: 15,                     // 默认缩放级别
    MAX_ZOOM: 18,                         // 最大缩放
    MIN_ZOOM: 10                          // 最小缩放
}
```

### 应用配置
```javascript
APP: {
    UPDATE_INTERVAL: 10000,    // 更新间隔(毫秒)
    MAX_TRACK_POINTS: 100,     // 最大轨迹点数
    AUTO_CENTER: true,         // 自动居中
    SHOW_TRACK: true          // 显示轨迹
}
```

## 🌐 跨网络访问

### 访问方式
- **手机4G/5G** → 实时查看位置
- **家庭WiFi** → 电脑浏览器访问
- **公司网络** → 远程监控
- **任何网络** → 通过域名访问

### 部署建议
1. **免费方案**: GitHub Pages + ThingSpeak
2. **自定义域名**: 购买域名绑定
3. **HTTPS加密**: 确保安全访问
4. **CDN加速**: 提高全球访问速度

## 🧪 测试模式

系统内置模拟数据生成器，无需真实硬件即可测试:

1. **自动模拟**: 连接失败时自动启用
2. **衡阳轨迹**: 模拟在衡阳市区的移动
3. **实时更新**: 模拟GPS数据变化
4. **历史轨迹**: 生成模拟历史数据

## 📱 移动端适配

- **响应式设计** - 自适应手机屏幕
- **触摸操作** - 支持手势缩放
- **移动优化** - 针对移动设备优化

## 🔧 故障排除

### 常见问题

1. **地图不显示**
   - 检查网络连接
   - 确认Leaflet.js加载成功

2. **数据不更新**
   - 检查ThingSpeak配置
   - 确认API密钥正确
   - 查看浏览器控制台错误

3. **跨域问题**
   - 使用本地服务器运行
   - 不要直接打开HTML文件

### 调试模式
在 `config.js` 中设置 `DEBUG: true` 查看详细日志

## 📞 技术支持

- **ThingSpeak文档**: https://www.mathworks.com/help/thingspeak/
- **Leaflet文档**: https://leafletjs.com/
- **OpenStreetMap**: https://www.openstreetmap.org/

## 🎯 下一步扩展

- [ ] 添加多设备支持
- [ ] 实现WebSocket实时推送
- [ ] 添加地理围栏功能
- [ ] 集成天气信息
- [ ] 添加轨迹分析功能
- [ ] 支持离线地图缓存

---

**🚗 现在您可以在任何网络上实时追踪衡阳的GPS位置了！**
