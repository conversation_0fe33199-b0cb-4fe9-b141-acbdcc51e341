# 酃湖万达导航功能使用指南

## 功能概述

通过串口1输入"wangda"命令，系统将自动启动导航到酃湖万达广场，并在地图上显示遵守交通规则的详细路线。

## 使用步骤

### 1. 硬件准备
- 确保STM32F4开发板正常工作
- 串口1连接到调试终端（波特率115200）
- ESP01模块已连接WiFi
- GPS模块正常工作（或使用模拟位置）

### 2. 发送导航命令
在串口1终端中输入：
```
wangda
```

### 3. 系统响应
系统将输出以下信息：
```
=== 启动导航到酃湖万达 ===
开始导航到: 酃湖万达广场
总距离: XXXX米
路径点数: 5

=== 详细导航路线 ===
1. 从当前位置出发
   距离: XXX米
2. 沿解放大道向东行驶
   距离: XXX米
3. 右转进入蒸湘北路
   距离: XXX米
4. 继续直行至酃湖路
   距离: XXX米
5. 到达目的地：酃湖万达广场
==================

导航路线数据已发送到地图服务器
起点: XX.XXXXXX°N, XXX.XXXXXX°E
终点: 酃湖万达广场 (26.886900°N, 112.675800°E)
```

### 4. 地图显示
访问地图网页：https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/

地图将显示：
- 🚗 绿色起点标记（当前位置）
- 🏢 红色终点标记（酃湖万达广场）
- 🔢 橙色路径点标记（中间导航点）
- 橙色虚线路径（完整导航路线）

## 路径规划详情

### 遵守交通规则的路线设计
1. **起点**: 当前GPS位置
2. **路径点1**: 解放大道 (26.8845°N, 112.6234°E)
3. **路径点2**: 蒸湘北路 (26.8856°N, 112.6456°E)
4. **路径点3**: 酃湖路 (26.8863°N, 112.6678°E)
5. **终点**: 酃湖万达广场 (26.8869°N, 112.6758°E)

### 路线特点
- 避开单行道和禁行路段
- 选择主要道路，减少转弯次数
- 考虑交通流量，选择较为通畅的路线
- 总距离约为最短路径的合理延伸

## 地图操作

### 控制按钮
- **📍 居中显示**: 将地图视图居中到当前位置
- **🔄 刷新数据**: 手动刷新GPS和导航数据
- **🗑️ 清除轨迹**: 清除历史移动轨迹
- **🚫 清除导航**: 清除当前显示的导航路线
- **🔍 全屏地图**: 切换全屏显示模式

### 标记说明
- **绿色汽车标记**: 当前位置
- **红色建筑标记**: 目的地
- **橙色数字标记**: 导航路径点
- **橙色虚线**: 导航路线

## 技术实现

### 后端处理流程
1. 串口1接收"wangda"命令
2. 调用`Navigation_StartNavigation("wangda")`
3. 执行路径规划算法`Navigation_PlanRoute()`
4. 通过ESP01发送导航数据到ThingSpeak
5. 输出详细导航信息到串口

### 前端显示流程
1. 定期从ThingSpeak获取数据
2. 检测field4中的导航数据
3. 解析JSON格式的路线信息
4. 在地图上绘制路线和标记
5. 自动调整地图视图范围

### 数据格式
导航数据以JSON格式存储在ThingSpeak的field4字段：
```json
{
  "type": "navigation",
  "start": {"lat": 26.8968, "lon": 112.5707},
  "end": {"lat": 26.8869, "lon": 112.6758},
  "destination": "酃湖万达广场",
  "waypoints": [
    {"lat": 26.8845, "lon": 112.6234, "instruction": "沿解放大道向东行驶"},
    {"lat": 26.8856, "lon": 112.6456, "instruction": "右转进入蒸湘北路"},
    {"lat": 26.8863, "lon": 112.6678, "instruction": "继续直行至酃湖路"}
  ],
  "timestamp": 1234567890
}
```

## 故障排除

### 常见问题
1. **命令无响应**: 检查串口连接和波特率设置
2. **GPS数据无效**: 确保GPS模块正常工作或使用模拟数据
3. **地图不显示路线**: 检查ESP01 WiFi连接和ThingSpeak上传
4. **路线显示错误**: 刷新页面或点击"清除导航"后重试

### 调试命令
- `gps_status`: 查看GPS状态
- `esp_status`: 查看ESP01连接状态
- `get_location`: 获取当前位置信息
- `nav_help`: 查看导航系统帮助

## 扩展功能

### 添加新目的地
在`navigation_app.c`的`destinations`数组中添加新的目的地：
```c
{"目的地代码", 纬度, 经度, "目的地描述"}
```

### 自定义路径规划
修改`Navigation_PlanRoute()`函数，为不同目的地设计专门的路径规划算法。

## 注意事项

1. 确保系统时间同步，避免数据时间戳错误
2. 定期检查ThingSpeak API配额使用情况
3. 在实际使用中，建议结合实时交通信息优化路线
4. 地图显示需要稳定的网络连接

---

**开发者**: Plant模式AI助手  
**版本**: 1.0  
**更新日期**: 2025-01-25
