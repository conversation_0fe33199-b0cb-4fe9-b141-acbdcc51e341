#!/usr/bin/env python3
"""
ESP01 GPS数据接收测试服务器
用于测试ESP01模块的TCP连接和GPS数据上传功能

使用方法:
1. 运行此脚本: python esp01_test_server.py
2. 服务器将在 0.0.0.0:8080 启动
3. ESP01可以连接到您的电脑IP地址的8080端口进行测试

功能:
- 接收ESP01发送的HTTP请求
- 解析GPS数据
- 显示详细的连接和数据信息
- 模拟ThingSpeak API响应
"""

import socket
import threading
import json
from datetime import datetime
import urllib.parse

class ESP01TestServer:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.running = False
        self.connections = 0
        self.gps_data_count = 0
        
    def start(self):
        """启动服务器"""
        self.running = True
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            server_socket.bind((self.host, self.port))
            server_socket.listen(5)
            
            print("=" * 60)
            print("🚀 ESP01 GPS测试服务器启动成功!")
            print(f"📡 监听地址: {self.host}:{self.port}")
            print(f"🌐 本地访问: http://localhost:{self.port}")
            print(f"📱 ESP01连接: 使用您的电脑IP地址:{self.port}")
            print("=" * 60)
            print("等待ESP01连接...")
            print()
            
            while self.running:
                try:
                    client_socket, address = server_socket.accept()
                    self.connections += 1
                    print(f"🔗 新连接 #{self.connections}: {address[0]}:{address[1]}")
                    
                    # 创建新线程处理客户端
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        print(f"❌ 服务器错误: {e}")
                        
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
        finally:
            server_socket.close()
            
    def handle_client(self, client_socket, address):
        """处理客户端连接"""
        try:
            # 接收数据
            data = client_socket.recv(4096).decode('utf-8', errors='ignore')
            
            if data:
                print(f"📨 收到来自 {address[0]} 的数据:")
                print("-" * 40)
                print(data)
                print("-" * 40)
                
                # 解析HTTP请求
                self.parse_http_request(data, address[0])
                
                # 发送HTTP响应
                response = self.create_http_response()
                client_socket.send(response.encode('utf-8'))
                
        except Exception as e:
            print(f"❌ 处理客户端 {address[0]} 时出错: {e}")
        finally:
            client_socket.close()
            print(f"🔌 连接 {address[0]} 已关闭\n")
            
    def parse_http_request(self, data, client_ip):
        """解析HTTP请求并提取GPS数据"""
        lines = data.split('\n')
        if not lines:
            return
            
        # 解析请求行
        request_line = lines[0].strip()
        print(f"📋 HTTP请求: {request_line}")
        
        # 提取GPS数据
        if 'GET' in request_line:
            if '?' in request_line:
                url_part = request_line.split(' ')[1]
                if '?' in url_part:
                    query_string = url_part.split('?')[1]
                    params = urllib.parse.parse_qs(query_string)
                    
                    # 检查是否包含GPS数据
                    if 'field1' in params and 'field2' in params:
                        self.gps_data_count += 1
                        lat = params['field1'][0] if 'field1' in params else 'N/A'
                        lon = params['field2'][0] if 'field2' in params else 'N/A'
                        alt = params['field3'][0] if 'field3' in params else 'N/A'
                        api_key = params['api_key'][0] if 'api_key' in params else 'N/A'
                        channel = params['channel'][0] if 'channel' in params else 'N/A'
                        
                        print(f"🛰️ GPS数据 #{self.gps_data_count}:")
                        print(f"   📍 纬度: {lat}")
                        print(f"   📍 经度: {lon}")
                        print(f"   📏 海拔: {alt}m")
                        print(f"   🔑 API密钥: {api_key}")
                        print(f"   📡 频道: {channel}")
                        print(f"   🕒 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                        
                        # 生成地图链接
                        if lat != 'N/A' and lon != 'N/A':
                            print(f"   🗺️ 地图: https://maps.google.com/?q={lat},{lon}")
                            
    def create_http_response(self):
        """创建HTTP响应"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 模拟ThingSpeak响应
        response_body = json.dumps({
            "status": "success",
            "message": "GPS data received successfully",
            "timestamp": timestamp,
            "server": "ESP01 Test Server",
            "connections": self.connections,
            "gps_count": self.gps_data_count
        }, indent=2)
        
        response = f"""HTTP/1.1 200 OK\r
Content-Type: application/json\r
Content-Length: {len(response_body)}\r
Connection: close\r
Server: ESP01-Test-Server/1.0\r
\r
{response_body}"""
        
        return response
        
    def stop(self):
        """停止服务器"""
        self.running = False
        print("\n🛑 服务器正在关闭...")

def main():
    """主函数"""
    server = ESP01TestServer()
    
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n\n🛑 收到停止信号...")
        server.stop()
        print("✅ 服务器已停止")

if __name__ == "__main__":
    main()
