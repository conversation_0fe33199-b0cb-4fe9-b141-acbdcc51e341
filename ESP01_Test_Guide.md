# ESP-01 WiFi连接测试指南

## 测试准备

### 硬件连接
1. **ESP-01连接到串口2**：
   - ESP-01 RX -> STM32 PA2 (UART2_TX)
   - ESP-01 TX -> STM32 PA3 (UART2_RX)
   - ESP-01 VCC -> 3.3V (确保电流足够，建议外接电源)
   - ESP-01 GND -> GND

2. **调试串口连接**：
   - STM32 PA9 (UART1_TX) -> USB转串口模块RX
   - STM32 PA10 (UART1_RX) -> USB转串口模块TX

### 软件准备
1. 串口调试工具（如PuTTY、串口助手等）
2. 波特率设置为115200
3. 确保WiFi网络"Tenda_ZC_5G"可用

## 测试步骤

### 1. 基本连接测试
1. 上电启动系统
2. 打开串口调试工具，连接到串口1
3. 观察启动信息，应该看到：
   ```
   ESP-01: Starting initialization...
   ESP-01: Sending AT test command
   ESP-01: Restarting module
   ESP-01: Setting WiFi mode to Station
   ESP-01: Connecting to WiFi: Tenda_ZC_5G
   ```

### 2. 响应监控测试
观察串口1输出，应该能看到ESP-01的响应：
```
ESP-01 Raw: AT
ESP-01 Response: OK
ESP-01 Raw: ready
ESP-01 Response: Module ready
ESP-01 Raw: WIFI CONNECTED
ESP-01 Response: WiFi connected!
ESP-01 Raw: WIFI GOT IP
ESP-01 Response: Got IP address!
```

### 3. 状态查询测试
在串口1发送命令测试：
- 发送：`esp_status` - 查看ESP-01当前状态
- 发送：`esp_reset` - 重置ESP-01模块
- 发送：`hello` - 测试原有功能

### 4. 连接成功验证
当看到以下信息时，表示连接成功：
```
ESP-01 Response: WiFi connected!
ESP-01 Response: Got IP address!
ESP-01: WiFi connected successfully!
```

## 预期输出示例

### 正常启动流程
```
ESP-01: Starting initialization...
ESP-01: Sending AT test command
ESP-01 Raw: AT
ESP-01 Response: OK
ESP-01: Restarting module
ESP-01 Raw: ready
ESP-01 Response: Module ready
ESP-01: Setting WiFi mode to Station
ESP-01 Raw: OK
ESP-01 Response: OK
ESP-01: Connecting to WiFi: Tenda_ZC_5G
ESP-01 Raw: WIFI CONNECTED
ESP-01 Response: WiFi connected!
ESP-01 Raw: WIFI GOT IP
ESP-01 Response: Got IP address!
ESP-01: WiFi connected successfully!
```

### 状态查询输出
发送`esp_status`后的输出：
```
ESP-01 Status: CONNECTED
```

## 故障排除

### 1. 没有看到ESP-01响应
**可能原因**：
- ESP-01硬件连接错误
- 电源供应不足
- ESP-01模块损坏

**解决方法**：
- 检查硬件连接
- 使用外部3.3V电源供电
- 更换ESP-01模块

### 2. 连接WiFi失败
**可能原因**：
- WiFi名称或密码错误
- WiFi信号太弱
- ESP-01不支持该WiFi加密方式

**解决方法**：
- 确认WiFi名称和密码
- 将设备移近路由器
- 检查WiFi加密方式（建议WPA2）

### 3. 频繁重连
**可能原因**：
- 电源不稳定
- WiFi信号不稳定
- ESP-01模块过热

**解决方法**：
- 使用稳定的电源
- 改善WiFi信号环境
- 增加散热措施

## 调试技巧

### 1. 原始数据监控
所有ESP-01的原始响应都会通过`ESP-01 Raw:`前缀显示，可以用来：
- 分析AT指令响应
- 调试通信问题
- 了解ESP-01状态

### 2. 手动AT指令测试
可以修改代码中的`esp01_SendCommand()`函数来发送自定义AT指令进行测试。

### 3. 时序调试
如果连接不稳定，可以调整代码中的延时参数：
- `ESP01_CMD_TIMEOUT`: 命令超时时间
- 初始化步骤间的延时时间

## 扩展功能

### 1. 添加更多AT指令
可以在`esp01_InitSequence()`中添加更多初始化步骤：
- 设置AP模式
- 配置TCP服务器
- 设置静态IP

### 2. 数据传输功能
基于当前框架，可以扩展：
- TCP客户端连接
- HTTP请求发送
- 数据上传到云服务器

### 3. 状态持久化
可以添加：
- 连接状态保存到Flash
- 自动重连机制优化
- 网络配置参数存储
