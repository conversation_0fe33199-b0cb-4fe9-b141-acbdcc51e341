#include "GPS_app.h"

uint16_t point1 = 0;
float longitude;
float latitude;

SaveData_t Save_Data;

LatitudeAndLongitude_t g_LatAndLongData = {
    .E_W = 0,
    .N_S = 0,
    .latitude = 0.0,
    .longitude = 0.0
};

char USART_RX_BUF[GPS_BUFFER_LENGTH];
uint8_t uart_GPS_RX_Buff;
extern uint8_t uart3_rx_dma_buffer[UART3_BUFFER_SIZE];
extern uint8_t uart3_ring_buffer_input[UART3_BUFFER_SIZE];
extern struct rt_ringbuffer uart3_ring_buffer;
extern uint8_t uart3_data_buffer[UART3_BUFFER_SIZE];

void GPS_Init(void)
{
    clrStruct();
    rt_ringbuffer_init(&uart3_ring_buffer, uart3_ring_buffer_input, UART3_BUFFER_SIZE);
    HAL_UARTEx_ReceiveToIdle_DMA(&huart3, uart3_rx_dma_buffer, UART3_BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_usart3_rx, DMA_IT_HT);
}

void GPS_Task(void)
{
    uint16_t data_len = rt_ringbuffer_data_len(&uart3_ring_buffer);
    static uint32_t last_gps_data_time = 0;
    static uint32_t last_status_report = 0;
    uint32_t current_time = HAL_GetTick();

    if (data_len > 0) {
        last_gps_data_time = current_time;
        rt_ringbuffer_get(&uart3_ring_buffer, uart3_data_buffer, data_len);

        // GPS原始数据输出（暂时注释以减少串口占用）
        // my_printf(&huart1, "GPS Raw data (%d bytes): ", data_len);
        // for (uint16_t i = 0; i < data_len && i < 50; i++) {
        //     if (uart3_data_buffer[i] >= 32 && uart3_data_buffer[i] <= 126) {
        //         my_printf(&huart1, "%c", uart3_data_buffer[i]);
        //     } else {
        //         my_printf(&huart1, "[%02X]", uart3_data_buffer[i]);
        //     }
        // }
        // my_printf(&huart1, "\r\n");
        
        for (uint16_t i = 0; i < data_len; i++) {
            uart_GPS_RX_Buff = uart3_data_buffer[i];
            if (uart_GPS_RX_Buff == '$') {
                point1 = 0;
            }
            USART_RX_BUF[point1++] = uart_GPS_RX_Buff;
            if (USART_RX_BUF[0] == '$' && USART_RX_BUF[4] == 'M' && USART_RX_BUF[5] == 'C') {
                if (uart_GPS_RX_Buff == '\n') {
                    for (uint16_t j = 0; j < GPS_BUFFER_LENGTH; j++) {
                        Save_Data.GPS_Buffer[j] = 0;
                    }
                    for (uint16_t j = 0; j < point1; j++) {
                        Save_Data.GPS_Buffer[j] = USART_RX_BUF[j];
                    }
                    Save_Data.isGetData = 1;
                    point1 = 0;
                    for (uint16_t j = 0; j < GPS_BUFFER_LENGTH; j++) {
                        USART_RX_BUF[j] = 0;
                    }
                }
            }
            if (point1 >= GPS_BUFFER_LENGTH) {
                point1 = GPS_BUFFER_LENGTH - 1;
            }
        }
    }
    parseGpsBuffer();
    printGpsBuffer();
    // GPS状态报告（暂时注释以减少串口占用）
    // if (current_time - last_status_report > 30000) {
    //     if (current_time - last_gps_data_time > 10000) {
    //         my_printf(&huart1, "GPS Status: No data received for %lu seconds\r\n",
    //                  (current_time - last_gps_data_time) / 1000);
    //         my_printf(&huart1, "Check GPS module connection and antenna\r\n");
    //     } else {
    //         my_printf(&huart1, "GPS Status: Receiving data normally\r\n");
    //     }
    //     last_status_report = current_time;
    // }
}

void GPS_Test_SimulateData(void)
{
    char test_nmea[] = "$GPRMC,123519.00,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A\r\n";
    for (uint16_t j = 0; j < GPS_BUFFER_LENGTH; j++) {
        Save_Data.GPS_Buffer[j] = 0;
    }
    uint16_t len = 0;
    while (test_nmea[len] != '\0' && len < GPS_BUFFER_LENGTH - 1) {
        Save_Data.GPS_Buffer[len] = test_nmea[len];
        len++;
    }
    Save_Data.isGetData = 1;
    parseGpsBuffer();
    printGpsBuffer();
}

void clrStruct(void)
{
    Save_Data.isGetData = 0;
    Save_Data.isParseData = 0;
    Save_Data.isUsefull = 0;
    for (uint16_t i = 0; i < GPS_BUFFER_LENGTH; i++) {
        Save_Data.GPS_Buffer[i] = 0;
    }
    for (uint16_t i = 0; i < UTCTIME_LENGTH; i++) {
        Save_Data.UTCTime[i] = 0;
    }
    for (uint16_t i = 0; i < LATITUDE_LENGTH; i++) {
        Save_Data.latitude[i] = 0;
    }
    for (uint16_t i = 0; i < N_S_LENGTH; i++) {
        Save_Data.N_S[i] = 0;
    }
    for (uint16_t i = 0; i < LONGITUDE_LENGTH; i++) {
        Save_Data.longitude[i] = 0;
    }
    for (uint16_t i = 0; i < E_W_LENGTH; i++) {
        Save_Data.E_W[i] = 0;
    }
}

void parseGpsBuffer(void)
{
    char *subString;
    char *subStringNext;
    char i = 0;
    uint16_t Number = 0, Integer = 0, Decimal = 0;
    if (Save_Data.isGetData)
    {
        Save_Data.isGetData = 0;
        for (i = 0; i <= 6; i++)
        {
            if (i == 0)
            {
                subString = Save_Data.GPS_Buffer;
                while (*subString != ',' && *subString != '\0') {
                    subString++;
                }
                if (*subString == '\0') return;
            }
            else
            {
                subString++;
                subStringNext = subString;
                while (*subStringNext != ',' && *subStringNext != '\0') {
                    subStringNext++;
                }
                if (*subStringNext != '\0')
                {
                    char usefullBuffer[2] = {0};
                    uint16_t len = subStringNext - subString;

                    switch (i)
                    {
                        case 1:
                            if (len < UTCTIME_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.UTCTime[j] = subString[j];
                                }
                            }
                            break;
                        case 2:
                            if (len < 2) {
                                usefullBuffer[0] = subString[0];
                            }
                            break;
                        case 3:
                            if (len < LATITUDE_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.latitude[j] = subString[j];
                                }
                            }
                            break;
                        case 4:
                            if (len < N_S_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.N_S[j] = subString[j];
                                }
                            }
                            break;
                        case 5:
                            if (len < LONGITUDE_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.longitude[j] = subString[j];
                                }
                            }
                            break;
                        case 6:
                            if (len < E_W_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.E_W[j] = subString[j];
                                }
                            }
                            break;
                        default:
                            break;
                    }
                    subString = subStringNext;
                    Save_Data.isParseData = 1;
                    if (usefullBuffer[0] == 'A')
                        Save_Data.isUsefull = 1;
                    else if (usefullBuffer[0] == 'V')
                        Save_Data.isUsefull = 0;
                }
            }
        }

        if (Save_Data.isParseData)
        {
            if (Save_Data.isUsefull)
            {
                g_LatAndLongData.N_S = Save_Data.N_S[0];
                g_LatAndLongData.E_W = Save_Data.E_W[0];
                for (uint8_t i = 0; i < 9; i++)
                {
                    if (Save_Data.latitude[i] == '\0') break;
                    if (i < 2)
                    {
                        Number *= 10;
                        Number += Save_Data.latitude[i] - '0';
                    }
                    else if (i < 4)
                    {
                        Integer *= 10;
                        Integer += Save_Data.latitude[i] - '0';
                    }
                    else if (i == 4);
                    else if (i < 9)
                    {
                        Decimal *= 10;
                        Decimal += Save_Data.latitude[i] - '0';
                    }
                }
                g_LatAndLongData.latitude = 1.0 * Number + (1.0 * Integer + 1.0 * Decimal / 10000) / 60;

                Number = 0;
                Integer = 0;
                Decimal = 0;
                for (uint8_t i = 0; i < 10; i++)
                {
                    if (Save_Data.longitude[i] == '\0') break;
                    if (i < 3)
                    {
                        Number *= 10;
                        Number += Save_Data.longitude[i] - '0';
                    }
                    else if (i < 5)
                    {
                        Integer *= 10;
                        Integer += Save_Data.longitude[i] - '0';
                    }
                    else if (i == 5);
                    else if (i < 10)
                    {
                        Decimal *= 10;
                        Decimal += Save_Data.longitude[i] - '0';
                    }
                }
                g_LatAndLongData.longitude = 1.0 * Number + (1.0 * Integer + 1.0 * Decimal / 10000) / 60;
                longitude = g_LatAndLongData.longitude;
                latitude = g_LatAndLongData.latitude;
                if (g_LatAndLongData.E_W == 'W')
                    longitude = -longitude;
                if (g_LatAndLongData.N_S == 'S')
                    latitude = -latitude;
            }
        }
    }
}

void printGpsBuffer(void)
{
    static uint32_t last_upload_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (Save_Data.isParseData)
    {
        Save_Data.isParseData = 0;
        my_printf(&huart1, "UTC Time = %s\r\n", Save_Data.UTCTime);
        if (Save_Data.isUsefull)
        {
            Save_Data.isUsefull = 0;
            // GPS解析输出（暂时注释以减少串口占用）
            // my_printf(&huart1, "Real GPS: Latitude: %c, %.6f\r\n", g_LatAndLongData.N_S, g_LatAndLongData.latitude);
            // my_printf(&huart1, "Real GPS: Longitude: %c, %.6f\r\n", g_LatAndLongData.E_W, g_LatAndLongData.longitude);
            // my_printf(&huart1, "Final GPS: %.6f, %.6f\r\n", latitude, longitude);
            // my_printf(&huart1, "------------------------\r\n");

            // 每5秒上传一次GPS数据到高德地图
            if (current_time - last_upload_time >= 5000) {
                GPS_UploadToAMap();
                last_upload_time = current_time;
            }
        }
        else
        {
            // my_printf(&huart1, "GPS DATA is not useful! (No satellite fix)\r\n");
        }
    }
}

typedef struct {
    float base_latitude;
    float base_longitude;
    float base_altitude;
    float movement_radius;
    uint8_t movement_enabled;
    uint32_t update_counter;
    uint8_t initialized;
} VirtualGPS_t;

static VirtualGPS_t virtual_gps = {0};

void GPS_Virtual_Init(void)
{
    virtual_gps.base_latitude = 26.8812f;
    virtual_gps.base_longitude = 112.6769f;
    virtual_gps.base_altitude = 65.0f;
    virtual_gps.movement_radius = 0.0f;
    virtual_gps.movement_enabled = 0;
    virtual_gps.update_counter = 0;
    virtual_gps.initialized = 1;
    clrStruct();
    my_printf(&huart1, "Virtual GPS initialized for Hengyang Normal University\r\n");
    my_printf(&huart1, "Base Location: %.5fN, %.5fE, %.1fm\r\n",
              virtual_gps.base_latitude, virtual_gps.base_longitude, virtual_gps.base_altitude);
}

void GPS_Virtual_SetLocation(float lat, float lon, float alt)
{
    if (!virtual_gps.initialized) {
        GPS_Virtual_Init();
    }
    virtual_gps.base_latitude = lat;
    virtual_gps.base_longitude = lon;
    virtual_gps.base_altitude = alt;
    my_printf(&huart1, "Virtual GPS location updated: %.6fN, %.6fE, %.1fm\r\n", lat, lon, alt);
}

void GPS_Virtual_EnableMovement(uint8_t enable)
{
    virtual_gps.movement_enabled = enable;
    my_printf(&huart1, "Virtual GPS movement: %s\r\n", enable ? "ENABLED" : "DISABLED");
}

void GPS_Virtual_GenerateData(void)
{
    if (!virtual_gps.initialized) {
        GPS_Virtual_Init();
    }
    // 使用衡阳师范学院的精确坐标
    float current_lat = 26.8812f;   // 衡阳师范学院纬度
    float current_lon = 112.6769f;  // 衡阳师范学院经度
    float current_alt = virtual_gps.base_altitude;
    uint32_t tick = HAL_GetTick();
    uint32_t seconds = (tick / 1000) % 86400;
    uint8_t hour = (seconds / 3600) % 24;
    uint8_t minute = (seconds / 60) % 60;
    uint8_t sec = seconds % 60;
    int lat_deg = (int)current_lat;
    float lat_min = (current_lat - lat_deg) * 60.0f;
    int lon_deg = (int)current_lon;
    float lon_min = (current_lon - lon_deg) * 60.0f;
    char nmea_sentence[200];
    snprintf(nmea_sentence, sizeof(nmea_sentence),
             "$GPRMC,%02d%02d%02d.00,A,%02d%07.4f,N,%03d%07.4f,E,0.0,0.0,220725,,,A*",
             hour, minute, sec,
             lat_deg, lat_min,
             lon_deg, lon_min);
    uint8_t checksum = 0;
    for (int i = 1; i < strlen(nmea_sentence) - 1; i++) {
        if (nmea_sentence[i] == '*') break;
        checksum ^= nmea_sentence[i];
    }
    char final_sentence[220];
    snprintf(final_sentence, sizeof(final_sentence), "%s%02X\r\n", nmea_sentence, checksum);

    strncpy(Save_Data.GPS_Buffer, final_sentence, GPS_BUFFER_LENGTH - 1);
    Save_Data.GPS_Buffer[GPS_BUFFER_LENGTH - 1] = '\0';
    Save_Data.isGetData = 1;
    latitude = current_lat;
    longitude = current_lon;
    g_LatAndLongData.latitude = current_lat;
    g_LatAndLongData.longitude = current_lon;
    g_LatAndLongData.N_S = 'N';
    g_LatAndLongData.E_W = 'E';
    virtual_gps.update_counter++;

    // 每次虚拟GPS更新时都上传到高德地图
    GPS_UploadToAMap();

    // 减少调试输出频率，每30秒输出一次
    if (virtual_gps.update_counter % 30 == 0) {
        my_printf(&huart1, "📍 Virtual GPS: Hengyang Normal University %.6f°N, %.6f°E, %.1fm [Update #%lu]\r\n",
                  current_lat, current_lon, current_alt, virtual_gps.update_counter);
        my_printf(&huart1, "🔒 Position: FIXED - High precision coordinates\r\n");
    }
}

/**
 * @brief 上传GPS数据到高德地图
 * 通过串口1发送格式化的GPS数据供PC端高德地图网页接收
 */
void GPS_UploadToAMap(void)
{
    static uint32_t upload_count = 0;
    char gps_data[128];
    float current_lat, current_lon, current_alt = 68.0;

    // 获取当前GPS位置数据
    if (g_LatAndLongData.latitude != 0.0 && g_LatAndLongData.longitude != 0.0) {
        // 使用实际GPS数据
        current_lat = latitude;
        current_lon = longitude;
    } else {
        // 使用衡阳市体育中心坐标作为默认位置
        current_lat = 26.885054837223990;
        current_lon = 112.679572502899990;
    }

    // 格式化GPS数据为高德地图网页可识别的格式
    snprintf(gps_data, sizeof(gps_data), "AMAP_GPS:%.6f,%.6f,%.1f", current_lat, current_lon, current_alt);

    // 通过串口1发送到PC端高德地图网页
    my_printf(&huart1, "%s\r\n", gps_data);

    upload_count++;

    // 每5次上传显示一次统计信息
    if (upload_count % 5 == 0) {
        my_printf(&huart1, "🗺️ GPS已上传到高德地图 #%lu: %.6f°N, %.6f°E\r\n",
                 upload_count, current_lat, current_lon);
        my_printf(&huart1, "🌐 高德地图API Key: 946b162f62c7b5af1892cc6fc00b2ea1\r\n");
    }
}

/**
 * @brief 手动触发GPS上传
 */
void GPS_ManualUpload(void)
{
    my_printf(&huart1, "🚀 手动触发GPS上传到高德地图...\r\n");
    GPS_UploadToAMap();
}

/**
 * @brief 设置自定义GPS位置并上传
 */
void GPS_SetCustomLocationAndUpload(float lat, float lon, float alt)
{
    // 更新全局GPS数据
    latitude = lat;
    longitude = lon;
    g_LatAndLongData.latitude = lat;
    g_LatAndLongData.longitude = lon;
    g_LatAndLongData.N_S = (lat >= 0) ? 'N' : 'S';
    g_LatAndLongData.E_W = (lon >= 0) ? 'E' : 'W';

    my_printf(&huart1, "📍 自定义位置已设置: %.6f°N, %.6f°E, %.1fm\r\n", lat, lon, alt);

    // 立即上传到高德地图
    GPS_UploadToAMap();
}

/**
 * @brief GPS上传状态报告
 */
void GPS_PrintUploadStatus(void)
{
    my_printf(&huart1, "\r\n=== GPS高德地图上传状态 ===\r\n");
    my_printf(&huart1, "📍 当前位置: %.6f°N, %.6f°E\r\n", latitude, longitude);
    my_printf(&huart1, "🗺️ 高德地图API Key: 946b162f62c7b5af1892cc6fc00b2ea1\r\n");
    my_printf(&huart1, "📊 ThingSpeak频道: 3014831 (API: LU22ZUP4ZTFK4IY9)\r\n");
    my_printf(&huart1, "⏰ 上传间隔: 5秒自动上传\r\n");
    my_printf(&huart1, "📱 数据格式: AMAP_GPS:纬度,经度,高度\r\n");
    my_printf(&huart1, "💡 命令: gps_upload (手动上传), gps_hengyang (设置体育中心)\r\n");
    my_printf(&huart1, "============================\r\n\r\n");
}
