#include "uart6_app.h"
#include "uart6_driver.h"
#include "uart_app.h"
#include <string.h>
#include <stdio.h>

extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart6;
extern DMA_HandleTypeDef hdma_usart6_rx;
extern uint8_t uart6_rx_dma_buffer[UART6_BUFFER_SIZE];
extern uint8_t uart6_ring_buffer_input[UART6_BUFFER_SIZE];
extern struct rt_ringbuffer uart6_ring_buffer;

void Uart6_Init(void)
{
  rt_ringbuffer_init(&uart6_ring_buffer, uart6_ring_buffer_input, UART6_BUFFER_SIZE);
  HAL_UARTEx_ReceiveToIdle_DMA(&huart6, uart6_rx_dma_buffer, UART6_BUFFER_SIZE);
  __HAL_DMA_DISABLE_IT(&hdma_usart6_rx, DMA_IT_HT);

  my_printf(&huart1, "✅ UART6 initialized successfully\r\n");
}

void Uart6_Task(void)
{
  // 简单的心跳输出
  static uint32_t last_heartbeat = 0;
  if (HAL_GetTick() - last_heartbeat >= 30000) {
    last_heartbeat = HAL_GetTick();
    my_printf(&huart1, "UART6 Task Heartbeat\r\n");
  }
}

void Uart6_RxCallback(uint16_t Size)
{
  // 简单处理，直接回显
  char response[100];
  if (Size > 0 && Size < sizeof(response)) {
    memcpy(response, uart6_rx_dma_buffer, Size);
    response[Size] = '\0';

    // 简单命令处理
    if (strncmp(response, "h", 1) == 0) {
      my_printf(&huart6, "hi\r\n");
    } else if (strncmp(response, "hello", 5) == 0) {
      my_printf(&huart6, "nihao\r\n");
    }
  }

  HAL_UARTEx_ReceiveToIdle_DMA(&huart6, uart6_rx_dma_buffer, UART6_BUFFER_SIZE);
}
