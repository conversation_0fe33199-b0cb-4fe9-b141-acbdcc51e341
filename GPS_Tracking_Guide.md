# ESP01 GPS实时追踪系统使用指南

## 🎯 系统功能

这是一个简化的GPS实时追踪系统，专注于：
- ✅ 实时GPS定位数据上传到ThingSpeak
- ✅ 在线地图显示当前位置
- ✅ 自动每15秒上传一次位置
- ✅ 支持手动上传测试

## 📋 您的ThingSpeak配置

- **频道ID**: 3014831
- **写入API密钥**: LU22ZUP4ZTFK4IY9
- **ThingSpeak IP**: ************* (最新)
- **地图链接**: https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/

## 🚀 快速开始

### 1. 系统初始化
```
esp_start
```
这会启动ESP01并连接WiFi网络

### 2. 检查系统状态
```
esp_status
```
确认系统状态为 `CONNECTED`

### 3. 测试GPS上传
```
gps_upload
```
手动上传一次GPS数据进行测试

### 4. 查看当前位置
```
get_location
```
显示当前GPS坐标

## 🔧 调试命令

### 基础诊断
```bash
esp_status          # 查看ESP01状态
esp_diag           # 完整网络诊断
get_location       # 获取当前GPS坐标
```

### 连接测试
```bash
esp_ip_test        # IP直连测试
esp_tcp_test       # TCP连接测试
test_thingspeak_api # ThingSpeak API测试
```

### GPS上传测试
```bash
gps_upload         # 手动GPS上传测试
send_location      # 发送位置数据
```

### 重置命令
```bash
esp_reset          # 软重置
esp_force_reset    # 强制重置模块
```

## 📊 系统工作流程

1. **启动阶段**:
   - ESP01模块初始化
   - 连接WiFi网络 (Tenda_ZC_5G)
   - 建立到ThingSpeak的TCP连接

2. **运行阶段**:
   - 每15秒自动获取GPS数据
   - 上传到ThingSpeak频道3014831
   - 地图实时更新显示位置

3. **数据格式**:
   - Field1: 纬度 (Latitude)
   - Field2: 经度 (Longitude) 
   - Field3: 海拔 (Altitude)

## 🗺️ 地图查看

访问您的实时地图：
https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/

地图会显示：
- 🔴 当前位置标记
- 📍 实时GPS轨迹
- ⏰ 最后更新时间

## ⚠️ 故障排除

### 如果GPS上传失败：

1. **检查网络连接**:
   ```
   esp_diag
   ```

2. **测试ThingSpeak连接**:
   ```
   test_thingspeak_api
   ```

3. **如果连接失败，重置系统**:
   ```
   esp_force_reset
   ```
   等待5秒后：
   ```
   esp_start
   ```

### 常见问题

**Q: 地图显示默认位置（衡阳师范学院）？**
A: 这表示GPS模块没有获取到有效定位，系统使用默认坐标。检查GPS天线和信号。

**Q: 上传成功但地图不更新？**
A: 等待1-2分钟，ThingSpeak有数据更新延迟。

**Q: TCP连接总是失败？**
A: 运行 `esp_ip_test` 测试网络连接，可能是DNS或防火墙问题。

## 📈 预期日志输出

**正常工作时的日志：**
```
✅ ESP-01: WiFi connected successfully!
✅ TCP connection established successfully!
📍 Location: [REAL GPS] Current Position
🌐 Coordinates: 26.886930N, 112.675813E, 50.0m
✅ GPS data upload successful!
🗺️ View Live Map: https://...
⏰ Next upload in 15 seconds
```

## 🎯 下一步测试

1. **立即执行**: `esp_start` 启动系统
2. **等待连接**: 观察WiFi和TCP连接状态
3. **测试上传**: `gps_upload` 手动测试
4. **查看地图**: 访问地图链接确认数据显示
5. **自动运行**: 系统会每15秒自动上传

系统现在专注于GPS追踪功能，移除了复杂的导航计算，应该更稳定可靠！
