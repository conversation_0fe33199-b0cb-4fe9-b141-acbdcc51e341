#ifndef __GPS_APP_H
#define __GPS_APP_H

#include "MyDefine.h"
#include "uart3_driver.h"

#define GPS_BUFFER_LENGTH   200
#define UTCTIME_LENGTH      11
#define LATITUDE_LENGTH     11
#define N_S_LENGTH          2
#define LONGITUDE_LENGTH    12
#define E_W_LENGTH          2

/* GPS数据保存结构体 */
typedef struct {
    char GPS_Buffer[GPS_BUFFER_LENGTH];     // GPS原始数据缓冲区
    char UTCTime[UTCTIME_LENGTH];           // UTC时间
    char latitude[LATITUDE_LENGTH];         // 纬度字符串
    char N_S[N_S_LENGTH];                   // 南北纬标识
    char longitude[LONGITUDE_LENGTH];       // 经度字符串
    char E_W[E_W_LENGTH];                   // 东西经标识
    uint8_t isGetData;                      // 是否获取到数据
    uint8_t isParseData;                    // 是否解析数据
    uint8_t isUsefull;                      // 数据是否有效
} SaveData_t;

/* 经纬度数据结构体 */
typedef struct {
    char E_W;               // 东西经标识
    char N_S;               // 南北纬标识
    float latitude;         // 纬度
    float longitude;        // 经度
} LatitudeAndLongitude_t;

/* 全局变量 */
extern SaveData_t Save_Data;
extern LatitudeAndLongitude_t g_LatAndLongData;
extern float longitude;
extern float latitude;

/* 函数声明 */
void GPS_Init(void);
void GPS_Task(void);
void parseGpsBuffer(void);
void printGpsBuffer(void);
void clrStruct(void);
void GPS_Test_SimulateData(void);  // 模拟GPS数据测试

/* 虚拟GPS数据生成器 */
void GPS_Virtual_Init(void);                    // 初始化虚拟GPS
void GPS_Virtual_GenerateData(void);            // 生成虚拟GPS数据
void GPS_Virtual_SetLocation(float lat, float lon, float alt);  // 设置基准位置
void GPS_Virtual_EnableMovement(uint8_t enable); // 启用/禁用移动模拟

/* GPS高德地图上传功能 */
void GPS_UploadToAMap(void);                    // 上传GPS数据到高德地图
void GPS_ManualUpload(void);                    // 手动触发GPS上传
void GPS_SetCustomLocationAndUpload(float lat, float lon, float alt);  // 设置自定义位置并上传
void GPS_PrintUploadStatus(void);               // 打印GPS上传状态

#endif

