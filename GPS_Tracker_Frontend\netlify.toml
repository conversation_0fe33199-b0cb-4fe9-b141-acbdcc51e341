# Netlify配置文件
[build]
  # 发布目录（当前目录）
  publish = "."

# 重定向规则
[[redirects]]
  # 单页应用支持
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# 头部设置
[[headers]]
  for = "/*"
  [headers.values]
    # 安全头部
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # 缓存控制
    Cache-Control = "public, max-age=3600"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=86400"

# 环境变量（可选）
[context.production.environment]
  NODE_ENV = "production"
