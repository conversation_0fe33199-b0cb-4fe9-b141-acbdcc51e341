<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航数据调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .data-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .data-section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 导航数据调试工具</h1>
        
        <div class="data-section">
            <h3>📡 ThingSpeak连接状态</h3>
            <div id="connection-status" class="status">正在检查连接...</div>
            <button onclick="checkConnection()">重新检查连接</button>
        </div>

        <div class="data-section">
            <h3>📊 最新GPS数据</h3>
            <div id="gps-status" class="status">正在获取数据...</div>
            <pre id="gps-data">等待数据...</pre>
            <button onclick="fetchLatestData()">刷新数据</button>
        </div>

        <div class="data-section">
            <h3>🗺️ 导航数据解析</h3>
            <div id="nav-status" class="status">等待导航数据...</div>
            <pre id="nav-data">等待导航数据...</pre>
        </div>

        <div class="data-section">
            <h3>🔄 自动刷新</h3>
            <button id="auto-refresh-btn" onclick="toggleAutoRefresh()">开启自动刷新</button>
            <span id="refresh-status">已停止</span>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/thingspeak.js"></script>
    <script>
        let autoRefreshInterval = null;
        let thingSpeakAPI = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            thingSpeakAPI = new ThingSpeakAPI();
            checkConnection();
            fetchLatestData();
        });

        // 检查连接
        async function checkConnection() {
            const statusEl = document.getElementById('connection-status');
            try {
                statusEl.textContent = '正在检查ThingSpeak连接...';
                statusEl.className = 'status';
                
                const response = await fetch(`${CONFIG.THINGSPEAK.BASE_URL}/channels/${CONFIG.THINGSPEAK.CHANNEL_ID}.json`);
                if (response.ok) {
                    statusEl.textContent = '✅ ThingSpeak连接正常';
                    statusEl.className = 'status success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.textContent = `❌ 连接失败: ${error.message}`;
                statusEl.className = 'status error';
            }
        }

        // 获取最新数据
        async function fetchLatestData() {
            const gpsStatusEl = document.getElementById('gps-status');
            const gpsDataEl = document.getElementById('gps-data');
            const navStatusEl = document.getElementById('nav-status');
            const navDataEl = document.getElementById('nav-data');

            try {
                gpsStatusEl.textContent = '正在获取最新数据...';
                gpsStatusEl.className = 'status';

                const data = await thingSpeakAPI.getLatestData();
                
                // 显示GPS数据
                gpsStatusEl.textContent = `✅ 数据获取成功 (${new Date().toLocaleTimeString()})`;
                gpsStatusEl.className = 'status success';
                gpsDataEl.textContent = JSON.stringify(data, null, 2);

                // 检查导航数据
                if (data.navigation) {
                    navStatusEl.textContent = '✅ 检测到导航数据';
                    navStatusEl.className = 'status success';
                    navDataEl.textContent = JSON.stringify(data.navigation, null, 2);
                } else {
                    navStatusEl.textContent = '⚠️ 未检测到导航数据';
                    navStatusEl.className = 'status error';
                    navDataEl.textContent = '无导航数据';
                }

            } catch (error) {
                gpsStatusEl.textContent = `❌ 获取数据失败: ${error.message}`;
                gpsStatusEl.className = 'status error';
                gpsDataEl.textContent = `错误: ${error.message}`;
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const btn = document.getElementById('auto-refresh-btn');
            const status = document.getElementById('refresh-status');

            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.textContent = '开启自动刷新';
                status.textContent = '已停止';
            } else {
                autoRefreshInterval = setInterval(fetchLatestData, 5000);
                btn.textContent = '停止自动刷新';
                status.textContent = '每5秒刷新';
            }
        }
    </script>
</body>
</html>
