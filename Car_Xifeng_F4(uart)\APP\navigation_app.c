#include "navigation_app.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

Navigation_t current_navigation = {0};
NavigationState_t nav_state = NAV_STATE_IDLE;

static const Destination_t destinations[MAX_DESTINATIONS] = {
    {"wangda", 26.8869f, 112.6758f, "酃湖万达广场"},
    {"wanda", 26.8869f, 112.6758f, "酃湖万达广场"},  // 添加wanda别名
    {"gaotie", 26.8945f, 112.6123f, "衡阳东高铁站"},
    {"daxue", 26.8812f, 112.6769f, "衡阳师范学院"},
    {"yiyuan", 26.8756f, 112.6234f, "南华大学附属第一医院"},
    {"gongyuan", 26.8934f, 112.5967f, "石鼓公园"},
    {"shangchang", 26.8823f, 112.6145f, "步步高购物中心"},
    {"jichang", 26.7345f, 112.6789f, "衡阳南岳机场"},
    {"xuexia<PERSON>", 26.8812f, 112.6769f, "衡阳师范学院(校区)"},
    {"zhongxin", 26.8912f, 112.6034f, "衡阳市中心"},
    {"huochezhan", 26.8834f, 112.6167f, "衡阳火车站"}
};

void Navigation_Init(void)
{
    nav_state = NAV_STATE_IDLE;
    memset(&current_navigation, 0, sizeof(Navigation_t));
    my_printf(&huart1, "����ϵͳ������������ nav_help �鿴����\r\n");
}

void Navigation_Task(void)
{
    static uint32_t last_update = 0;
    uint32_t current_time = HAL_GetTick();
    
    if (current_time - last_update < 1000) return;
    last_update = current_time;
    
    switch (nav_state) {
        case NAV_STATE_NAVIGATING:
            Navigation_UpdateProgress();
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart1, "�ѵ���Ŀ�ĵ�: %s\r\n", current_navigation.destination.description);
            nav_state = NAV_STATE_IDLE;
            break;
        default:
            break;
    }
}

uint8_t Navigation_StartNavigation(const char* destination_name)
{
    // 在串口6中显示调试信息
    my_printf(&huart6, "🔍 正在查找目的地: %s\r\n", destination_name);

    Destination_t dest;
    if (!Navigation_FindDestination(destination_name, &dest)) {
        my_printf(&huart6, "❌ 未找到目的地: %s\r\n", destination_name);
        my_printf(&huart1, "δ�ҵ�Ŀ�ĵ�: %s\r\n", destination_name);
        return 0;
    }

    my_printf(&huart6, "✅ 找到目的地: %s\r\n", dest.description);

    float current_lat = g_LatAndLongData.latitude;
    float current_lon = g_LatAndLongData.longitude;

    my_printf(&huart6, "📍 当前GPS坐标: %.6f°N, %.6f°E\r\n", current_lat, current_lon);

    if (current_lat == 0.0f || current_lon == 0.0f) {
        my_printf(&huart6, "❌ GPS信号无效，无法开始导航\r\n");
        my_printf(&huart6, "💡 请等待GPS信号稳定后重试\r\n");
        my_printf(&huart1, "GPS�ź���Ч���޷���ʼ����\r\n");
        return 0;
    }

    my_printf(&huart6, "✅ GPS信号有效，开始路径规划...\r\n");
    
    current_navigation.destination = dest;
    current_navigation.current_waypoint = 0;
    current_navigation.is_active = 1;
    current_navigation.is_arrived = 0;

    my_printf(&huart6, "🛣️ 开始详细路径规划...\r\n");
    Navigation_PlanRoute(current_lat, current_lon, dest.latitude, dest.longitude);

    nav_state = NAV_STATE_NAVIGATING;

    my_printf(&huart6, "🎯 导航启动成功!\r\n");
    my_printf(&huart6, "📏 总距离: %.0f米\r\n", current_navigation.total_distance);

    my_printf(&huart1, "��ʼ������: %s\r\n", dest.description);
    my_printf(&huart1, "�ܾ���: %.0f��\r\n", current_navigation.total_distance);

    // 上传GPS位置数据到ThingSpeak (使用持久连接)
    extern void esp01_SendLocationData(void);
    esp01_SendLocationData();

    return 1;
}

void Navigation_StopNavigation(void)
{
    nav_state = NAV_STATE_IDLE;
    current_navigation.is_active = 0;
    my_printf(&huart1, "������ֹͣ\r\n");
}

void Navigation_ProcessCommand(const char* command)
{
    if (strncmp(command, "nav_", 4) == 0) {
        const char* nav_cmd = command + 4;
        
        if (strcmp(nav_cmd, "help") == 0) {
            my_printf(&huart1, "����ϵͳ����:\r\n");
            my_printf(&huart1, "nav_help - ��ʾ����\r\n");
            my_printf(&huart1, "nav_list - ��ʾĿ�ĵ��б�\r\n");
            my_printf(&huart1, "nav_status - ��ʾ����״̬\r\n");
            my_printf(&huart1, "nav_stop - ֹͣ����\r\n");
            my_printf(&huart1, "wangda - ������۹�����\r\n");
            my_printf(&huart1, "gaotie - ����������վ\r\n");
        }
        else if (strcmp(nav_cmd, "list") == 0) {
            Navigation_PrintDestinations();
        }
        else if (strcmp(nav_cmd, "status") == 0) {
            Navigation_PrintStatus();
        }
        else if (strcmp(nav_cmd, "stop") == 0) {
            Navigation_StopNavigation();
        }
    }
    else {
        // ����Ƿ�ΪĿ�ĵ�����
        uint8_t found = 0;
        for (int i = 0; i < MAX_DESTINATIONS; i++) {
            if (strcmp(command, destinations[i].name) == 0) {
                Navigation_StartNavigation(command);
                found = 1;
                return;
            }
        }

        // ������ǵ������������ʾ
        if (!found && strlen(command) > 0) {
            my_printf(&huart1, "δ֪����: %s\r\n", command);
            my_printf(&huart1, "���� nav_help �鿴����\r\n");
        }
    }
}

uint8_t Navigation_FindDestination(const char* name, Destination_t* dest)
{
    for (int i = 0; i < MAX_DESTINATIONS; i++) {
        if (strcmp(name, destinations[i].name) == 0) {
            *dest = destinations[i];
            return 1;
        }
    }
    return 0;
}

void Navigation_PlanRoute(float start_lat, float start_lon, float end_lat, float end_lon)
{
    // 检查是否是到万达的导航，使用详细路径规划
    if (fabs(end_lat - 26.8869f) < 0.001f && fabs(end_lon - 112.6758f) < 0.001f) {
        Navigation_PlanWandaRoute(start_lat, start_lon);
        return;
    }

    // 默认简单路径规划
    current_navigation.waypoint_count = 2;
    
    current_navigation.waypoints[0].latitude = start_lat;
    current_navigation.waypoints[0].longitude = start_lon;
    strcpy(current_navigation.waypoints[0].instruction, "�ӵ�ǰλ�ó���");

    current_navigation.waypoints[1].latitude = end_lat;
    current_navigation.waypoints[1].longitude = end_lon;
    strcpy(current_navigation.waypoints[1].instruction, "����Ŀ�ĵ�");
    
    float distance = Navigation_CalculateDistance(start_lat, start_lon, end_lat, end_lon);
    float bearing = Navigation_CalculateBearing(start_lat, start_lon, end_lat, end_lon);
    
    current_navigation.waypoints[0].distance_to_next = distance;
    current_navigation.waypoints[0].bearing_to_next = bearing;
    current_navigation.total_distance = distance;
    current_navigation.remaining_distance = distance;
    
    const char* direction = "";
    if (bearing >= 337.5f || bearing < 22.5f) direction = "��";
    else if (bearing >= 22.5f && bearing < 67.5f) direction = "����";
    else if (bearing >= 67.5f && bearing < 112.5f) direction = "��";
    else if (bearing >= 112.5f && bearing < 157.5f) direction = "����";
    else if (bearing >= 157.5f && bearing < 202.5f) direction = "��";
    else if (bearing >= 202.5f && bearing < 247.5f) direction = "����";
    else if (bearing >= 247.5f && bearing < 292.5f) direction = "��";
    else direction = "����";
    
    snprintf(current_navigation.waypoints[0].instruction, 128,
             "��%s������ʻ %.0f��", direction, distance);
}

float Navigation_CalculateDistance(float lat1, float lon1, float lat2, float lon2)
{
    float dlat = (lat2 - lat1) * M_PI / 180.0f;
    float dlon = (lon2 - lon1) * M_PI / 180.0f;
    float a = sinf(dlat/2) * sinf(dlat/2) + cosf(lat1 * M_PI / 180.0f) * 
              cosf(lat2 * M_PI / 180.0f) * sinf(dlon/2) * sinf(dlon/2);
    float c = 2 * atan2f(sqrtf(a), sqrtf(1-a));
    return EARTH_RADIUS * c;
}

float Navigation_CalculateBearing(float lat1, float lon1, float lat2, float lon2)
{
    float dlon = (lon2 - lon1) * M_PI / 180.0f;
    float lat1_rad = lat1 * M_PI / 180.0f;
    float lat2_rad = lat2 * M_PI / 180.0f;
    
    float y = sinf(dlon) * cosf(lat2_rad);
    float x = cosf(lat1_rad) * sinf(lat2_rad) - sinf(lat1_rad) * cosf(lat2_rad) * cosf(dlon);
    
    float bearing = atan2f(y, x) * 180.0f / M_PI;
    return fmodf(bearing + 360.0f, 360.0f);
}

void Navigation_UpdateProgress(void)
{
    float current_lat = g_LatAndLongData.latitude;
    float current_lon = g_LatAndLongData.longitude;
    
    if (current_lat == 0.0f || current_lon == 0.0f) return;
    
    float distance_to_dest = Navigation_CalculateDistance(
        current_lat, current_lon,
        current_navigation.destination.latitude,
        current_navigation.destination.longitude
    );
    
    current_navigation.remaining_distance = distance_to_dest;
    
    if (distance_to_dest < 50.0f) {
        nav_state = NAV_STATE_ARRIVED;
        current_navigation.is_arrived = 1;
        return;
    }
    
    static uint32_t last_instruction = 0;
    if (HAL_GetTick() - last_instruction > 10000) {
        float bearing = Navigation_CalculateBearing(
            current_lat, current_lon,
            current_navigation.destination.latitude,
            current_navigation.destination.longitude
        );
        
        const char* direction = "";
        if (bearing >= 337.5f || bearing < 22.5f) direction = "��";
        else if (bearing >= 22.5f && bearing < 67.5f) direction = "����";
        else if (bearing >= 67.5f && bearing < 112.5f) direction = "��";
        else if (bearing >= 112.5f && bearing < 157.5f) direction = "����";
        else if (bearing >= 157.5f && bearing < 202.5f) direction = "��";
        else if (bearing >= 202.5f && bearing < 247.5f) direction = "����";
        else if (bearing >= 247.5f && bearing < 292.5f) direction = "��";
        else direction = "����";
        
        my_printf(&huart1, "������%s������ʻ������%.0f�׵���%s\r\n",
                  direction, distance_to_dest, current_navigation.destination.description);
        
        last_instruction = HAL_GetTick();
    }
}

void Navigation_PrintStatus(void)
{
    // 在串口6中显示状态
    my_printf(&huart6, "Navigation Status:\r\n");

    switch (nav_state) {
        case NAV_STATE_IDLE:
            my_printf(&huart6, "Status: IDLE (Ready)\r\n");
            my_printf(&huart6, "System: Ready for navigation commands\r\n");
            break;
        case NAV_STATE_NAVIGATING:
            my_printf(&huart6, "Status: NAVIGATING\r\n");
            my_printf(&huart6, "Destination: %s\r\n", current_navigation.destination.description);
            my_printf(&huart6, "Remaining Distance: %.0f meters\r\n", current_navigation.remaining_distance);
            my_printf(&huart6, "Waypoints: %d\r\n", current_navigation.waypoint_count);
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart6, "Status: ARRIVED\r\n");
            my_printf(&huart6, "Destination: %s\r\n", current_navigation.destination.description);
            break;
        default:
            my_printf(&huart6, "Status: UNKNOWN\r\n");
            break;
    }

    // 显示GPS状态
    extern LatitudeAndLongitude_t g_LatAndLongData;
    my_printf(&huart6, "GPS Status:\r\n");
    my_printf(&huart6, "Latitude: %.6f N\r\n", g_LatAndLongData.latitude);
    my_printf(&huart6, "Longitude: %.6f E\r\n", g_LatAndLongData.longitude);

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "����״̬:\r\n");
    switch (nav_state) {
        case NAV_STATE_IDLE:
            my_printf(&huart1, "״̬: ����\r\n");
            break;
        case NAV_STATE_NAVIGATING:
            my_printf(&huart1, "״̬: ������\r\n");
            my_printf(&huart1, "Ŀ�ĵ�: %s\r\n", current_navigation.destination.description);
            my_printf(&huart1, "ʣ�����: %.0f��\r\n", current_navigation.remaining_distance);
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart1, "״̬: �ѵ���\r\n");
            break;
        default:
            my_printf(&huart1, "״̬: δ֪\r\n");
            break;
    }
}

void Navigation_PrintDestinations(void)
{
    // 在串口6中显示目的地列表
    my_printf(&huart6, "Available Destinations:\r\n");
    for (int i = 0; i < MAX_DESTINATIONS && destinations[i].name[0] != '\0'; i++) {
        my_printf(&huart6, "  %s - %s\r\n", destinations[i].name, destinations[i].description);
    }

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "����Ŀ�ĵ�:\r\n");
    for (int i = 0; i < MAX_DESTINATIONS && destinations[i].name[0] != '\0'; i++) {
        my_printf(&huart1, "%s - %s\r\n", destinations[i].name, destinations[i].description);
    }
}

/**
 * 万达专用路径规划 - 遵守交通规则的详细路线
 */
void Navigation_PlanWandaRoute(float start_lat, float start_lon)
{
    // 在串口6中显示路径规划开始
    my_printf(&huart6, "\r\n🎯 ========== 详细路径规划 ==========\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "\r\n🎯 ========== 启动万达导航路径规划 ==========\r\n");

    // 定义遵守交通规则的路径点
    typedef struct {
        float lat;
        float lon;
        char instruction[128];
        char road_name[64];
    } RoutePoint_t;

    RoutePoint_t route_points[] = {
        {start_lat, start_lon, "从衡阳师范学院出发", "起点"},
        {26.8825f, 112.6745f, "沿校园路向东行驶300米", "校园路"},
        {26.8840f, 112.6750f, "右转进入解放大道", "解放大道"},
        {26.8855f, 112.6755f, "沿解放大道向东北行驶500米", "解放大道"},
        {26.8865f, 112.6758f, "到达酃湖万达广场", "酃湖万达广场"}
    };

    int route_count = sizeof(route_points) / sizeof(RoutePoint_t);
    current_navigation.waypoint_count = route_count;

    float total_distance = 0.0f;

    // 设置路径点并计算距离
    for (int i = 0; i < route_count; i++) {
        current_navigation.waypoints[i].latitude = route_points[i].lat;
        current_navigation.waypoints[i].longitude = route_points[i].lon;
        strcpy(current_navigation.waypoints[i].instruction, route_points[i].instruction);

        if (i < route_count - 1) {
            float segment_distance = Navigation_CalculateDistance(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            current_navigation.waypoints[i].distance_to_next = segment_distance;
            current_navigation.waypoints[i].bearing_to_next = Navigation_CalculateBearing(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            total_distance += segment_distance;
        } else {
            current_navigation.waypoints[i].distance_to_next = 0.0f;
            current_navigation.waypoints[i].bearing_to_next = 0.0f;
        }
    }

    current_navigation.total_distance = total_distance;
    current_navigation.remaining_distance = total_distance;

    // 在串口6中输出详细导航信息
    my_printf(&huart6, "🚗 目的地：酃湖万达广场\r\n");
    my_printf(&huart6, "📏 总距离：%.0f米\r\n", total_distance);
    my_printf(&huart6, "🛣️ 路径点数：%d个\r\n", route_count);
    my_printf(&huart6, "\r\n=== 详细导航路线 ===\r\n");

    for (int i = 0; i < route_count; i++) {
        if (i < route_count - 1) {
            my_printf(&huart6, "%d. %s\r\n", i+1, route_points[i].instruction);
            my_printf(&huart6, "   距离: %.0f米 | 道路: %s\r\n",
                     current_navigation.waypoints[i].distance_to_next, route_points[i].road_name);
        } else {
            my_printf(&huart6, "%d. %s\r\n", i+1, route_points[i].instruction);
        }
    }

    my_printf(&huart6, "==================\r\n");
    my_printf(&huart6, "🗺️ 导航路线数据已准备完成\r\n");
    my_printf(&huart6, "📍 起点: %.6f°N, %.6f°E\r\n", start_lat, start_lon);
    my_printf(&huart6, "🏢 终点: 26.886900°N, 112.675800°E\r\n");
    my_printf(&huart6, "==========================================\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "🚗 目的地：酃湖万达广场\r\n");
    my_printf(&huart1, "📏 总距离：%.0f米\r\n", total_distance);
    my_printf(&huart1, "🛣️ 路径点数：%d个\r\n", route_count);
    my_printf(&huart1, "\r\n=== 详细导航路线 ===\r\n");

    for (int i = 0; i < route_count; i++) {
        if (i < route_count - 1) {
            my_printf(&huart1, "%d. %s\r\n", i+1, route_points[i].instruction);
            my_printf(&huart1, "   距离: %.0f米 | 道路: %s\r\n",
                     current_navigation.waypoints[i].distance_to_next, route_points[i].road_name);
        } else {
            my_printf(&huart1, "%d. %s\r\n", i+1, route_points[i].instruction);
        }
    }

    my_printf(&huart1, "==================\r\n");
    my_printf(&huart1, "🗺️ 导航路线数据已准备完成\r\n");
    my_printf(&huart1, "📍 起点: %.6f°N, %.6f°E\r\n", start_lat, start_lon);
    my_printf(&huart1, "🏢 终点: 26.886900°N, 112.675800°E\r\n");
    my_printf(&huart1, "==========================================\r\n\r\n");

    // 上传导航数据到地图服务器
    Navigation_UploadRouteToMap();
}

/**
 * 上传路线数据到地图服务器
 */
void Navigation_UploadRouteToMap(void)
{
    // 在串口6中显示上传信息
    my_printf(&huart6, "\r\n📡 正在上传导航路线到地图服务器...\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "📡 正在上传导航路线到地图服务器...\r\n");

    // 构建路线数据JSON格式 - 兼容前端解析
    char route_data[1024];
    snprintf(route_data, sizeof(route_data),
        "{"
        "\"type\":\"navigation\","
        "\"destination\":\"酃湖万达广场\","
        "\"start\":{\"lat\":%.6f,\"lng\":%.6f},"
        "\"end\":{\"lat\":26.886900,\"lng\":112.675800},"
        "\"waypoints\":["
        "{\"lat\":%.6f,\"lng\":%.6f,\"instruction\":\"从衡阳师范学院出发\"},"
        "{\"lat\":26.882500,\"lng\":112.674500,\"instruction\":\"沿校园路向东行驶300米\"},"
        "{\"lat\":26.884000,\"lng\":112.675000,\"instruction\":\"右转进入解放大道\"},"
        "{\"lat\":26.885500,\"lng\":112.675500,\"instruction\":\"沿解放大道向东北行驶500米\"},"
        "{\"lat\":26.886900,\"lng\":112.675800,\"instruction\":\"到达酃湖万达广场\"}"
        "],"
        "\"total_distance\":%.0f,"
        "\"distance\":%.0f"
        "}",
        current_navigation.waypoints[0].latitude,
        current_navigation.waypoints[0].longitude,
        current_navigation.waypoints[0].latitude,
        current_navigation.waypoints[0].longitude,
        current_navigation.total_distance,
        current_navigation.total_distance
    );

    // 通过ESP01发送到ThingSpeak
    extern void esp01_SendNavigationData(const char* route_data);
    esp01_SendNavigationData(route_data);

    // 在串口6中显示完成信息
    my_printf(&huart6, "✅ 导航路线数据已发送到地图服务器\r\n");
    my_printf(&huart6, "🌐 地图将显示详细导航路线\r\n");
    my_printf(&huart6, "🎯 导航系统准备就绪！\r\n\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "✅ 导航路线数据已发送到地图服务器\r\n");
}