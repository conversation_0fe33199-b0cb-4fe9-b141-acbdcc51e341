<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ThingSpeak连接测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        .status {
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .config-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        .data-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 ThingSpeak连接测试工具</h1>
        
        <!-- 配置检查 -->
        <div class="test-section">
            <h3>📋 1. 配置检查</h3>
            <div id="config-status" class="status info">正在检查配置...</div>
            <div class="config-display" id="config-display"></div>
        </div>

        <!-- 连接测试 -->
        <div class="test-section">
            <h3>🌐 2. ThingSpeak连接测试</h3>
            <button onclick="testConnection()">测试连接</button>
            <div id="connection-status" class="status info">点击按钮测试连接</div>
        </div>

        <!-- 数据获取测试 -->
        <div class="test-section">
            <h3>📡 3. 数据获取测试</h3>
            <button onclick="testDataFetch()">获取最新数据</button>
            <button onclick="testHistoryFetch()">获取历史数据</button>
            <div id="data-status" class="status info">点击按钮测试数据获取</div>
            <div class="data-display" id="data-display"></div>
        </div>

        <!-- 模拟数据测试 -->
        <div class="test-section">
            <h3>🎭 4. 模拟数据测试</h3>
            <button onclick="testMockData()">生成模拟数据</button>
            <div id="mock-status" class="status info">点击按钮生成模拟数据</div>
            <div class="data-display" id="mock-display"></div>
        </div>

        <!-- 部署指南 -->
        <div class="test-section">
            <h3>🚀 5. 部署指南</h3>
            <p><strong>本地测试：</strong></p>
            <div class="config-display">
                # Python服务器<br>
                python -m http.server 8000<br>
                # 访问：http://localhost:8000<br><br>
                
                # Node.js服务器<br>
                npx http-server<br>
                # 访问：http://localhost:8080
            </div>
            
            <p><strong>云端部署：</strong></p>
            <ul>
                <li><strong>GitHub Pages：</strong> 免费，适合开源项目</li>
                <li><strong>Netlify：</strong> 拖拽部署，自动HTTPS</li>
                <li><strong>Vercel：</strong> 开发者友好，Git集成</li>
            </ul>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <script src="js/config.js"></script>
    <script src="js/thingspeak.js"></script>

    <script>
        // 页面加载时检查配置
        window.onload = function() {
            checkConfiguration();
        };

        // 检查配置
        function checkConfiguration() {
            const configStatus = document.getElementById('config-status');
            const configDisplay = document.getElementById('config-display');
            
            try {
                const config = CONFIG.THINGSPEAK;
                let html = `
                    <strong>ThingSpeak配置：</strong><br>
                    Channel ID: ${config.CHANNEL_ID}<br>
                    Read API Key: ${config.READ_API_KEY}<br>
                    Base URL: ${config.BASE_URL}<br><br>
                    
                    <strong>地图配置：</strong><br>
                    中心坐标: ${CONFIG.MAP.DEFAULT_CENTER}<br>
                    默认缩放: ${CONFIG.MAP.DEFAULT_ZOOM}<br><br>
                    
                    <strong>应用配置：</strong><br>
                    更新间隔: ${CONFIG.APP.UPDATE_INTERVAL}ms<br>
                    设备ID: ${CONFIG.APP.DEVICE.ID}
                `;
                
                configDisplay.innerHTML = html;
                
                // 检查是否为示例配置
                if (config.CHANNEL_ID === '2747890' || config.READ_API_KEY === 'ABCDEFGHIJKLMNOP') {
                    configStatus.className = 'status error';
                    configStatus.textContent = '⚠️ 检测到示例配置，请更新为您的实际ThingSpeak密钥';
                } else {
                    configStatus.className = 'status success';
                    configStatus.textContent = '✅ 配置检查通过';
                }
                
            } catch (error) {
                configStatus.className = 'status error';
                configStatus.textContent = `❌ 配置检查失败: ${error.message}`;
            }
        }

        // 测试连接
        async function testConnection() {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 正在测试连接...';
            
            try {
                const isConnected = await thingSpeakAPI.checkConnection();
                if (isConnected) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ ThingSpeak连接成功！';
                } else {
                    throw new Error('连接失败');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 连接失败: ${error.message}`;
            }
        }

        // 测试数据获取
        async function testDataFetch() {
            const statusDiv = document.getElementById('data-status');
            const displayDiv = document.getElementById('data-display');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 正在获取最新数据...';
            
            try {
                const data = await thingSpeakAPI.getLatestData();
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 数据获取成功！';
                
                displayDiv.innerHTML = `
                    <strong>最新GPS数据：</strong><br>
                    时间: ${data.timestamp}<br>
                    纬度: ${data.latitude}<br>
                    经度: ${data.longitude}<br>
                    海拔: ${data.altitude}m<br>
                    Entry ID: ${data.entry_id}
                `;
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 数据获取失败: ${error.message}`;
                displayDiv.innerHTML = '';
            }
        }

        // 测试历史数据获取
        async function testHistoryFetch() {
            const statusDiv = document.getElementById('data-status');
            const displayDiv = document.getElementById('data-display');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 正在获取历史数据...';
            
            try {
                const data = await thingSpeakAPI.getHistoryData(10);
                statusDiv.className = 'status success';
                statusDiv.textContent = `✅ 获取到${data.length}条历史数据！`;
                
                let html = '<strong>历史GPS数据：</strong><br>';
                data.forEach((item, index) => {
                    html += `${index + 1}. ${item.timestamp} - ${item.latitude}, ${item.longitude}<br>`;
                });
                displayDiv.innerHTML = html;
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 历史数据获取失败: ${error.message}`;
                displayDiv.innerHTML = '';
            }
        }

        // 测试模拟数据
        function testMockData() {
            const statusDiv = document.getElementById('mock-status');
            const displayDiv = document.getElementById('mock-display');
            
            try {
                const mockData = mockDataGenerator.generateMockData();
                const historyData = mockDataGenerator.generateHistoryData(5);
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 模拟数据生成成功！';
                
                let html = `
                    <strong>当前模拟数据：</strong><br>
                    时间: ${mockData.timestamp}<br>
                    纬度: ${mockData.latitude} (衡阳)<br>
                    经度: ${mockData.longitude} (衡阳)<br>
                    海拔: ${mockData.altitude}m<br><br>
                    
                    <strong>历史模拟数据：</strong><br>
                `;
                
                historyData.forEach((item, index) => {
                    html += `${index + 1}. ${item.latitude}, ${item.longitude}<br>`;
                });
                
                displayDiv.innerHTML = html;
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 模拟数据生成失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
