/**
 * ThingSpeak代理服务器
 * 
 * 功能：
 * 1. 接收ESP01的GPS数据
 * 2. 转发到ThingSpeak API
 * 3. 返回结果给ESP01
 * 
 * 部署：可以部署到任何支持Node.js的云服务器
 */

const express = require('express');
const https = require('https');
const http = require('http');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// 启用CORS
app.use(cors());
app.use(express.json());

// ThingSpeak配置
const THINGSPEAK_HOST = 'api.thingspeak.com';
const THINGSPEAK_PORT = 443;

/**
 * 代理路由 - 转发GPS数据到ThingSpeak
 */
app.get('/thingspeak-proxy', async (req, res) => {
    try {
        const { api_key, field1, field2, field3 } = req.query;
        
        console.log(`📍 收到GPS数据: lat=${field1}, lon=${field2}, alt=${field3}`);
        
        // 构建ThingSpeak URL
        const thingspeakUrl = `/update?api_key=${api_key}&field1=${field1}&field2=${field2}&field3=${field3}`;
        
        // 转发到ThingSpeak
        const options = {
            hostname: THINGSPEAK_HOST,
            port: THINGSPEAK_PORT,
            path: thingspeakUrl,
            method: 'GET',
            headers: {
                'User-Agent': 'ESP01-GPS-Proxy/1.0'
            }
        };
        
        const proxyRequest = https.request(options, (proxyResponse) => {
            let data = '';
            
            proxyResponse.on('data', (chunk) => {
                data += chunk;
            });
            
            proxyResponse.on('end', () => {
                console.log(`✅ ThingSpeak响应: ${data}`);
                res.json({
                    success: true,
                    thingspeak_response: data,
                    timestamp: new Date().toISOString(),
                    gps_data: {
                        latitude: field1,
                        longitude: field2,
                        altitude: field3
                    }
                });
            });
        });
        
        proxyRequest.on('error', (error) => {
            console.error(`❌ ThingSpeak请求失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        });
        
        proxyRequest.end();
        
    } catch (error) {
        console.error(`❌ 代理服务器错误: ${error.message}`);
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

/**
 * 健康检查路由
 */
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'ThingSpeak GPS Proxy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

/**
 * 测试路由
 */
app.get('/test', (req, res) => {
    res.json({
        message: 'ThingSpeak代理服务器运行正常',
        usage: '/thingspeak-proxy?api_key=YOUR_KEY&field1=LAT&field2=LON&field3=ALT',
        example: '/thingspeak-proxy?api_key=LU22ZUP4ZTFK4IY9&field1=28.2282&field2=112.9388&field3=50.0',
        timestamp: new Date().toISOString()
    });
});

/**
 * 根路由
 */
app.get('/', (req, res) => {
    res.send(`
        <h1>🌐 ThingSpeak GPS代理服务器</h1>
        <p><strong>状态</strong>: 运行中</p>
        <p><strong>功能</strong>: 转发ESP01 GPS数据到ThingSpeak</p>
        <p><strong>API端点</strong>: <code>/thingspeak-proxy</code></p>
        <p><strong>测试</strong>: <a href="/test">/test</a></p>
        <p><strong>健康检查</strong>: <a href="/health">/health</a></p>
        <hr>
        <h3>使用方法:</h3>
        <code>GET /thingspeak-proxy?api_key=YOUR_KEY&field1=LAT&field2=LON&field3=ALT</code>
        <h3>示例:</h3>
        <code>GET /thingspeak-proxy?api_key=LU22ZUP4ZTFK4IY9&field1=28.2282&field2=112.9388&field3=50.0</code>
    `);
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 ThingSpeak代理服务器启动成功`);
    console.log(`📡 监听端口: ${PORT}`);
    console.log(`🌐 访问地址: http://localhost:${PORT}`);
    console.log(`🎯 代理目标: ${THINGSPEAK_HOST}`);
    console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});
