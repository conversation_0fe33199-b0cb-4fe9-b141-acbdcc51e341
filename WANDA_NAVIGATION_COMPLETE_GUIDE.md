# 🎯 酃湖万达导航系统完整使用指南

## 🚀 功能概述

通过串口6输入"wanda"命令，系统将自动启动从衡阳师范学院到酃湖万达广场的智能导航，并在地图上显示遵守交通规则的详细路线。

## 📋 系统特性

### ✅ 智能路径规划
- **起点**: 衡阳师范学院 (26.8812°N, 112.6769°E)
- **终点**: 酃湖万达广场 (26.8869°N, 112.6758°E)
- **路径点**: 5个详细导航点，遵守交通规则
- **总距离**: 约800米，预计步行10分钟

### ✅ 多串口支持
- **串口1 (UART1)**: 主调试串口，显示详细导航信息
- **串口6 (UART6)**: 导航命令输入串口，支持"wanda"命令

### ✅ 实时地图显示
- **前端地图**: https://687f487cfe09505bd19a25c6--relaxed-zabaione-3eabb4.netlify.app/
- **路线可视化**: 橙色虚线显示完整导航路径
- **路径点标记**: 数字标记显示中间导航点
- **起终点标记**: 绿色汽车(起点) + 红色建筑(终点)

## 🛣️ 详细路线规划

### 路径设计 (遵守交通规则)
1. **起点**: 衡阳师范学院 (26.8812°N, 112.6769°E)
2. **路径点1**: 校园路东段 (26.8825°N, 112.6745°E)
   - 指令: "沿校园路向东行驶300米"
3. **路径点2**: 解放大道入口 (26.8840°N, 112.6750°E)
   - 指令: "右转进入解放大道"
4. **路径点3**: 解放大道东段 (26.8855°N, 112.6755°E)
   - 指令: "沿解放大道向东北行驶500米"
5. **终点**: 酃湖万达广场 (26.8869°N, 112.6758°E)
   - 指令: "到达酃湖万达广场"

### 路线特点
- ✅ **避开单行道**: 选择双向通行道路
- ✅ **主要道路优先**: 使用解放大道等主干道
- ✅ **减少转弯**: 最小化复杂路口转弯
- ✅ **安全第一**: 避开施工路段和危险区域

## 🎮 使用方法

### 方法1: 串口6命令 (推荐)
```bash
# 通过串口6发送命令 (波特率115200)
wanda
```

### 方法2: 串口1命令 (备用)
```bash
# 通过串口1发送命令 (波特率115200)
wangda
```

## 📺 系统响应

### 串口1输出 (详细信息)
```
🎯 ========== UART6导航命令：酃湖万达 ==========
🚗 目的地：酃湖万达广场
📍 坐标：26.8869°N, 112.6758°E
🛣️ 路线：遵守交通规则的最优路径
🗺️ 正在规划路线并上传到地图...
📡 数据来源：串口6 (UART6)
==========================================

🎯 ========== 启动万达导航路径规划 ==========
🚗 目的地：酃湖万达广场
📏 总距离：800米
🛣️ 路径点数：5个

=== 详细导航路线 ===
1. 从衡阳师范学院出发
   距离: 300米 | 道路: 起点
2. 沿校园路向东行驶300米
   距离: 200米 | 道路: 校园路
3. 右转进入解放大道
   距离: 150米 | 道路: 解放大道
4. 沿解放大道向东北行驶500米
   距离: 150米 | 道路: 解放大道
5. 到达酃湖万达广场
==================

🗺️ 导航路线数据已准备完成
📍 起点: 26.881200°N, 112.676900°E
🏢 终点: 26.886900°N, 112.675800°E
==========================================

📡 正在上传导航路线到地图服务器...
✅ 导航路线数据已发送到地图服务器
🌐 地图将显示详细导航路线
```

### 串口6输出 (确认信息)
```
Navigation to Wanda Plaza started!
```

## 🗺️ 地图显示效果

访问前端地图后，您将看到：

### 🎨 视觉元素
- **🚗 绿色汽车标记**: 起点 (衡阳师范学院)
- **🏢 红色建筑标记**: 终点 (酃湖万达广场)
- **1️⃣2️⃣3️⃣ 橙色数字标记**: 中间路径点
- **🔶 橙色虚线**: 完整导航路径

### 🎛️ 交互功能
- **📍 居中显示**: 自动调整视图显示完整路线
- **🔍 缩放控制**: 支持地图放大缩小
- **💬 信息弹窗**: 点击标记查看详细信息
- **🔄 实时更新**: 自动刷新GPS位置

## 🔧 技术实现

### 后端处理流程
1. **串口6接收**: "wanda"命令 → UART6处理
2. **路径规划**: `Navigation_PlanWandaRoute()` 生成详细路径
3. **数据上传**: ESP01发送JSON格式路线数据到ThingSpeak
4. **地图渲染**: 前端解析数据并显示导航路线

### 数据传输格式
```json
{
  "type": "navigation",
  "destination": "酃湖万达广场",
  "start": {"lat": 26.881200, "lng": 112.676900},
  "end": {"lat": 26.886900, "lng": 112.675800},
  "waypoints": [
    {"lat": 26.881200, "lng": 112.676900, "instruction": "从衡阳师范学院出发"},
    {"lat": 26.882500, "lng": 112.674500, "instruction": "沿校园路向东行驶300米"},
    {"lat": 26.884000, "lng": 112.675000, "instruction": "右转进入解放大道"},
    {"lat": 26.885500, "lng": 112.675500, "instruction": "沿解放大道向东北行驶500米"},
    {"lat": 26.886900, "lng": 112.675800, "instruction": "到达酃湖万达广场"}
  ],
  "total_distance": 800
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. 串口6无响应
- **检查连接**: 确认串口6硬件连接正确
- **波特率设置**: 确认波特率为115200
- **命令格式**: 确保输入"wanda"(小写)

#### 2. 地图不显示路线
- **网络连接**: 检查ESP01 WiFi连接状态
- **ThingSpeak上传**: 确认数据成功上传到频道3014831
- **页面刷新**: 刷新地图页面或清除浏览器缓存

#### 3. 路径显示错误
- **GPS定位**: 确认当前位置为衡阳师范学院
- **数据同步**: 等待10-15秒让数据完全同步
- **清除导航**: 点击"清除导航"按钮后重新发送命令

### 调试命令
```bash
# 基础测试
hello                    # 测试串口6连接
esp_debug               # 检查ESP01状态
get_location            # 获取当前GPS位置

# 导航测试
wanda                   # 启动万达导航 (串口6)
wangda                  # 启动万达导航 (串口1)
nav_status              # 查看导航状态
nav_stop                # 停止当前导航
```

## 🎯 使用建议

### 最佳实践
1. **首次使用**: 先用"hello"命令测试串口6连接
2. **网络检查**: 确保ESP01已连接WiFi并能访问ThingSpeak
3. **地图准备**: 提前打开地图页面，等待GPS数据加载
4. **命令输入**: 输入"wanda"后等待系统完成路径规划

### 扩展功能
- **添加新目的地**: 在destinations数组中添加新坐标
- **自定义路径**: 修改Navigation_PlanWandaRoute函数
- **语音导航**: 可集成TTS模块实现语音播报
- **实时交通**: 可接入交通API获取实时路况

---

**🎉 恭喜！您的万达导航系统已完全配置完成！**

现在您可以通过串口6输入"wanda"命令，享受智能导航服务了！
