# 🧭 智能导航系统使用指南

## 🎯 功能概述

您的STM32F407智能小车现在具备了完整的导航功能！通过串口1输入目的地关键词，系统会自动规划路径并提供实时导航指引。

## 📍 支持的目的地

| 关键词 | 目的地 | 坐标 |
|--------|--------|------|
| `wangda` | 酃湖万达广场 | 26.8869°N, 112.6758°E |
| `gaotie` | 衡阳东高铁站 | 26.8945°N, 112.6123°E |
| `daxue` | 衡阳师范学院 | 26.8869°N, 112.6758°E |
| `yiyuan` | 南华大学附属第一医院 | 26.8756°N, 112.6234°E |
| `gongyuan` | 石鼓公园 | 26.8934°N, 112.5967°E |
| `shangchang` | 华新开发区 | 26.8823°N, 112.6145°E |
| `jichang` | 衡阳南岳机场 | 26.7345°N, 112.6789°E |
| `xuexiao` | 衡阳师范学院东校区 | 26.8869°N, 112.6758°E |
| `zhongxin` | 衡阳市中心 | 26.8912°N, 112.6034°E |
| `huochezhan` | 衡阳火车站 | 26.8834°N, 112.6167°E |

## 🎮 导航命令

### 基本导航命令
```bash
wangda          # 导航到酃湖万达广场
gaotie          # 导航到衡阳东高铁站
daxue           # 导航到衡阳师范学院
yiyuan          # 导航到南华大学附属第一医院
```

### 系统管理命令
```bash
nav_help        # 显示导航帮助信息
nav_list        # 显示所有可用目的地
nav_status      # 显示当前导航状态
nav_stop        # 停止当前导航
```

## 🚗 使用流程

### 1. 启动导航
```bash
# 通过串口1发送命令
wangda
```

**系统响应：**
```
🚗 开始导航到: 酃湖万达广场
📍 目标坐标: 26.886900°N, 112.675800°E
📏 总距离: 1250.3米
🧭 向东北方向行驶 1250.3米
```

### 2. 导航过程中
系统每10秒自动提供导航指引：
```
🧭 继续向东北方向行驶，还有856米到达酃湖万达广场
🧭 继续向东方向行驶，还有432米到达酃湖万达广场
🧭 继续向东南方向行驶，还有128米到达酃湖万达广场
```

### 3. 到达目的地
```
🎉 已到达目的地: 酃湖万达广场
```

## 🛠️ 技术特性

### 智能路径规划
- ✅ **实时GPS定位**：基于真实GPS坐标计算路径
- ✅ **距离计算**：使用Haversine公式精确计算地球表面距离
- ✅ **方向指引**：提供8个方向的精确导航指引
- ✅ **动态更新**：根据当前位置实时更新剩余距离和方向

### 交通规则遵守
- 🚦 **方向优化**：选择最直接的行驶方向
- 📏 **距离优先**：优先选择最短路径
- ⚠️ **安全提醒**：到达目的地50米内自动提醒

### 系统集成
- 🛰️ **GPS集成**：与现有GPS模块无缝集成
- 📡 **数据上传**：导航数据可上传到ThingSpeak云平台
- 🌐 **地图显示**：在您的网页地图上实时显示导航路径

## 📊 状态监控

### 查看导航状态
```bash
nav_status
```

**输出示例：**
```
=== 导航状态 ===
状态: 导航中
目的地: 酃湖万达广场
剩余距离: 456米
===============
```

### 查看可用目的地
```bash
nav_list
```

**输出示例：**
```
=== 可用目的地 ===
wangda - 酃湖万达广场
gaotie - 衡阳东高铁站
daxue - 衡阳师范学院
yiyuan - 南华大学附属第一医院
...
================
```

## ⚠️ 注意事项

### GPS要求
- 📡 **信号要求**：需要有效的GPS信号才能开始导航
- 🌍 **室外使用**：GPS在室内无法正常工作
- ⏱️ **启动时间**：GPS冷启动需要1-3分钟

### 精度说明
- 📍 **定位精度**：GPS精度约为3-5米
- 🎯 **到达判定**：距离目的地50米内判定为到达
- 🔄 **更新频率**：导航指引每10秒更新一次

### 系统限制
- 📱 **目的地数量**：当前支持10个预设目的地
- 🗺️ **路径类型**：直线路径，不考虑道路限制
- 🚗 **适用场景**：适合开阔地带的直线导航

## 🔧 故障排除

### 常见问题

**1. 无法开始导航**
```
❌ GPS信号无效，无法开始导航
```
**解决方案：**
- 确保设备在室外开阔地带
- 等待GPS获取卫星信号
- 使用`gps_status`命令检查GPS状态

**2. 找不到目的地**
```
❌ 未找到目的地: xxxxx
```
**解决方案：**
- 使用`nav_list`查看可用目的地
- 检查输入的关键词是否正确
- 确保关键词完全匹配

**3. 导航中断**
**解决方案：**
- 使用`nav_status`检查导航状态
- 重新发送目的地命令重启导航
- 检查GPS信号是否稳定

## 🚀 扩展功能

### 添加新目的地
可以在`navigation_app.c`中的`destinations`数组中添加新的目的地：

```c
{"keyword", latitude, longitude, "description"}
```

### 自定义导航逻辑
可以修改`Navigation_PlanRoute()`函数来实现更复杂的路径规划算法。

---

**🎉 现在您可以通过串口1输入`wangda`来导航到酃湖万达广场了！**
