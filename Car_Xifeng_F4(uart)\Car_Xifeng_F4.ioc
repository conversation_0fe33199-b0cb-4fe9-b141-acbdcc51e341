#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART1_RX
Dma.Request1=USART2_RX
Dma.Request2=USART3_RX
Dma.Request3=USART6_RX
Dma.RequestsNb=4
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.1.Instance=DMA1_Stream5
Dma.USART2_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.1.Mode=DMA_NORMAL
Dma.USART2_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.1.Priority=DMA_PRIORITY_HIGH
Dma.USART2_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.2.Instance=DMA1_Stream1
Dma.USART3_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.2.Mode=DMA_NORMAL
Dma.USART3_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.2.Priority=DMA_PRIORITY_HIGH
Dma.USART3_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART6_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART6_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART6_RX.3.Instance=DMA2_Stream1
Dma.USART6_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART6_RX.3.MemInc=DMA_MINC_ENABLE
Dma.USART6_RX.3.Mode=DMA_NORMAL
Dma.USART6_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART6_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART6_RX.3.Priority=DMA_PRIORITY_HIGH
Dma.USART6_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C2.I2C_Mode=I2C_Fast
I2C2.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=I2C2
Mcu.IP10=USART2
Mcu.IP11=USART3
Mcu.IP12=USART6
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IP5=TIM1
Mcu.IP6=TIM3
Mcu.IP7=TIM4
Mcu.IP8=UART4
Mcu.IP9=USART1
Mcu.IPNb=13
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PB2
Mcu.Pin11=PE9
Mcu.Pin12=PE11
Mcu.Pin13=PE13
Mcu.Pin14=PE14
Mcu.Pin15=PB10
Mcu.Pin16=PB11
Mcu.Pin17=PD8
Mcu.Pin18=PD9
Mcu.Pin19=PD12
Mcu.Pin2=PC14-OSC32_IN
Mcu.Pin20=PD13
Mcu.Pin21=PD15
Mcu.Pin22=PC6
Mcu.Pin23=PC7
Mcu.Pin24=PC8
Mcu.Pin25=PC9
Mcu.Pin26=PA9
Mcu.Pin27=PA10
Mcu.Pin28=PA11
Mcu.Pin29=PA12
Mcu.Pin3=PC15-OSC32_OUT
Mcu.Pin30=PA13
Mcu.Pin31=PA14
Mcu.Pin32=PB4
Mcu.Pin33=PB5
Mcu.Pin34=PE0
Mcu.Pin35=PE1
Mcu.Pin36=VP_SYS_VS_Systick
Mcu.Pin37=VP_TIM1_VS_ClockSourceINT
Mcu.Pin4=PH0-OSC_IN
Mcu.Pin5=PH1-OSC_OUT
Mcu.Pin6=PA0-WKUP
Mcu.Pin7=PA1
Mcu.Pin8=PA2
Mcu.Pin9=PA3
Mcu.PinsNb=38
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UART4_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Mode=Asynchronous
PA0-WKUP.Signal=UART4_TX
PA1.Mode=Asynchronous
PA1.Signal=UART4_RX
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=PinState,GPIO_Label
PA11.GPIO_Label=LED1
PA11.Locked=true
PA11.PinState=GPIO_PIN_SET
PA11.Signal=GPIO_Output
PA12.GPIOParameters=PinState,GPIO_Label
PA12.GPIO_Label=LED2
PA12.Locked=true
PA12.PinState=GPIO_PIN_SET
PA12.Signal=GPIO_Output
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Locked=true
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Locked=true
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB2.GPIOParameters=PinState,GPIO_Label
PB2.GPIO_Label=greenLed
PB2.Locked=true
PB2.PinState=GPIO_PIN_SET
PB2.Signal=GPIO_Output
PB4.Locked=true
PB4.Signal=S_TIM3_CH1
PB5.Locked=true
PB5.Signal=S_TIM3_CH2
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PC8.GPIOParameters=PinState,GPIO_Label
PC8.GPIO_Label=LED4
PC8.Locked=true
PC8.PinState=GPIO_PIN_SET
PC8.Signal=GPIO_Output
PC9.GPIOParameters=PinState,GPIO_Label
PC9.GPIO_Label=LED3
PC9.Locked=true
PC9.PinState=GPIO_PIN_SET
PC9.Signal=GPIO_Output
PD12.Locked=true
PD12.Signal=S_TIM4_CH1
PD13.Locked=true
PD13.Signal=S_TIM4_CH2
PD15.GPIOParameters=GPIO_Label
PD15.GPIO_Label=AF_KEY
PD15.Locked=true
PD15.Signal=GPIO_Input
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE0.GPIOParameters=GPIO_Label
PE0.GPIO_Label=KEY1
PE0.Locked=true
PE0.Signal=GPIO_Input
PE1.GPIOParameters=GPIO_Label
PE1.GPIO_Label=KEY2
PE1.Locked=true
PE1.Signal=GPIO_Input
PE11.Locked=true
PE11.Signal=S_TIM1_CH2
PE13.Locked=true
PE13.Signal=GPIO_Output
PE14.Locked=true
PE14.Signal=S_TIM1_CH4
PE2.GPIOParameters=GPIO_Label
PE2.GPIO_Label=KEY3
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=KEY4
PE3.Locked=true
PE3.Signal=GPIO_Input
PE9.Locked=true
PE9.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x600
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Car_Xifeng_F4.ioc
ProjectManager.ProjectName=Car_Xifeng_F4
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x1000
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_TIM1_Init-TIM1-false-HAL-true,6-MX_TIM3_Init-TIM3-false-HAL-true,7-MX_TIM4_Init-TIM4-false-HAL-true,8-MX_I2C2_Init-I2C2-false-HAL-true,9-MX_USART2_UART_Init-USART2-false-HAL-true,10-MX_USART3_UART_Init-USART3-false-HAL-true,11-MX_UART4_Init-UART4-false-HAL-true,12-MX_USART6_UART_Init-USART6-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Prescaler,Period,Channel-PWM Generation2 CH2,Channel-PWM Generation4 CH4
TIM1.Period=100-1
TIM1.Prescaler=168-1
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IPParameters=EncoderMode
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IPParameters=EncoderMode
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
board=custom
