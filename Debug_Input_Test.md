# 🔧 串口输入调试指南

## 问题排查步骤

### 1. 基础连接测试
首先测试串口1是否能正常接收数据：

**输入：** `hello`
**预期输出：** 
```
Received: [hello] (len=5)
shabi
```

如果看到这个输出，说明串口1接收正常。

### 2. 导航命令测试
**输入：** `wangda`
**预期输出：**
```
Received: [wangda] (len=6)
开始导航到: 酃湖万达广场
总距离: XXXX米
```

### 3. 帮助命令测试
**输入：** `nav_help`
**预期输出：**
```
Received: [nav_help] (len=8)
导航系统命令:
nav_help - 显示帮助
nav_list - 显示目的地列表
nav_status - 显示导航状态
nav_stop - 停止导航
wangda - 导航到酃湖万达
gaotie - 导航到高铁站
```

### 4. 未知命令测试
**输入：** `test123`
**预期输出：**
```
Received: [test123] (len=7)
未知命令: test123
输入 nav_help 查看帮助
```

## 🚨 常见问题及解决方案

### 问题1: 完全没有输出
**可能原因：**
- 串口连接问题
- 波特率设置错误
- 程序没有运行

**解决方案：**
1. 检查串口连接线
2. 确认波特率设置为115200
3. 检查程序是否正常烧录和运行

### 问题2: 看到"Received"但没有命令响应
**可能原因：**
- 命令格式问题
- 回车符问题
- GPS信号问题（导航命令）

**解决方案：**
1. 确保命令后发送回车符(\r\n)
2. 检查命令拼写是否正确
3. 对于导航命令，确保GPS有信号

### 问题3: GPS信号无效导致导航失败
**输入：** `wangda`
**可能输出：**
```
Received: [wangda] (len=6)
GPS信号无效，无法开始导航
```

**解决方案：**
1. 将设备移到室外开阔地带
2. 等待GPS获取卫星信号
3. 使用`gps_status`检查GPS状态

## 🔍 调试命令列表

### 系统状态检查
```
gps_status      # 检查GPS状态
esp_status      # 检查ESP01状态
get_location    # 获取当前位置
```

### 导航系统检查
```
nav_help        # 显示导航帮助
nav_list        # 显示可用目的地
nav_status      # 显示导航状态
```

### 基础功能测试
```
hello           # 测试串口基础功能
send_location   # 测试位置上传
```

## 📋 完整测试流程

### 步骤1: 基础连接测试
```
输入: hello
期望: 看到 "Received: [hello]" 和 "shabi"
```

### 步骤2: GPS状态检查
```
输入: gps_status
期望: 看到GPS详细状态信息
```

### 步骤3: 导航系统测试
```
输入: nav_help
期望: 看到导航命令列表
```

### 步骤4: 目的地列表检查
```
输入: nav_list
期望: 看到所有可用目的地
```

### 步骤5: 导航功能测试
```
输入: wangda
期望: 开始导航或提示GPS信号问题
```

## 🛠️ 串口助手设置

### 推荐设置
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控**: 无
- **发送格式**: 文本模式
- **接收格式**: 文本模式
- **自动发送回车换行**: 开启

### 发送格式
确保每个命令后都有回车换行符：
```
wangda\r\n
nav_help\r\n
hello\r\n
```

---

**请按照这个流程逐步测试，并告诉我在哪一步出现了问题！**
