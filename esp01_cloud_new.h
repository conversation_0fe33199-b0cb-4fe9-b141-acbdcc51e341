/**
 * ESP01 新云服务连接模块头文件
 * 
 * Author: AI Assistant
 * Date: 2025-01-27
 */

#ifndef __ESP01_CLOUD_NEW_H
#define __ESP01_CLOUD_NEW_H

#include "MyDefine.h"

// 云服务状态枚举
typedef enum {
    CLOUD_STATE_IDLE = 0,
    CLOUD_STATE_CONNECTING,
    CLOUD_STATE_CONNECTED,
    CLOUD_STATE_UPLOADING,
    CLOUD_STATE_ERROR
} CloudState_t;

// 函数声明
void CloudService_Init(void);
uint8_t CloudService_TestBasicConnection(void);
uint8_t CloudService_Connect(void);
uint8_t CloudService_UploadGPS(float lat, float lon, float alt);
uint8_t CloudService_SimpleUpload(float lat, float lon, float alt);
void CloudService_Task(void);
CloudState_t CloudService_GetState(void);
void CloudService_Reset(void);

#endif /* __ESP01_CLOUD_NEW_H */
