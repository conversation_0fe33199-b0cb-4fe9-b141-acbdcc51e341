#ifndef __MPU6050_APP_H__
#define __MPU6050_APP_H__

#include "main.h"
#include "i2c.h"
#include "usart.h"
#include <stdint.h>
#include <string.h>
#include <math.h>
#include <stdio.h>
#include <stdarg.h>
#include "mpu6050_driver.h"

/* 姿态角结构体 */
typedef struct {
    float roll;     // 横滚角 (°)
    float pitch;    // 俯仰角 (°)
    float yaw;      // 偏航角 (°)
} MPU6050_Attitude_t;

/* 卡尔曼滤波器结构体 */
typedef struct {
    float Q_angle;      // 过程噪声协方差 - 角度
    float Q_bias;       // 过程噪声协方差 - 偏差
    float R_measure;    // 测量噪声协方差
    float angle;        // 滤波后的角度
    float bias;         // 陀螺仪偏差
    float rate;         // 角速度
    float P[2][2];      // 误差协方差矩阵
} KalmanFilter_t;

/* MPU6050应用层结构体 */
typedef struct {
    MPU6050_t mpu_device;           // MPU6050设备
    MPU6050_Attitude_t attitude;    // 姿态角
    KalmanFilter_t kalman_roll;     // 横滚角卡尔曼滤波器
    KalmanFilter_t kalman_pitch;    // 俯仰角卡尔曼滤波器
    
    // 校准参数
    float accel_offset_x;           // 加速度计X轴偏移
    float accel_offset_y;           // 加速度计Y轴偏移
    float accel_offset_z;           // 加速度计Z轴偏移
    float gyro_offset_x;            // 陀螺仪X轴偏移
    float gyro_offset_y;            // 陀螺仪Y轴偏移
    float gyro_offset_z;            // 陀螺仪Z轴偏移
    
    // 状态标志
    uint8_t is_calibrated;          // 校准完成标志
    uint8_t data_ready;             // 数据就绪标志
    
    // 统计信息
    uint32_t read_count;            // 读取次数
    uint32_t error_count;           // 错误次数
} MPU6050_App_t;

/* 全局MPU6050应用实例 */
extern MPU6050_App_t mpu6050_app;

/* 函数声明 */

/* 初始化和配置 */
uint8_t MPU6050_App_Init(void);
uint8_t MPU6050_App_SetConfig(MPU6050_GyroRange_t gyro_range, MPU6050_AccelRange_t accel_range);

/* 数据读取和处理 */
uint8_t MPU6050_App_ReadData(void);
void MPU6050_App_Task(void);

/* 校准功能 */
uint8_t MPU6050_App_Calibrate(uint16_t samples);
void MPU6050_App_ApplyCalibration(void);

/* 姿态计算 */
void MPU6050_App_CalculateAttitude(void);
float MPU6050_App_KalmanFilter(KalmanFilter_t *kalman, float new_angle, float new_rate, float dt);

/* 数据获取接口 */
MPU6050_Data_t* MPU6050_App_GetData(void);
MPU6050_RawData_t* MPU6050_App_GetRawData(void);
MPU6050_Attitude_t* MPU6050_App_GetAttitude(void);

/* 状态查询 */
uint8_t MPU6050_App_IsReady(void);
uint8_t MPU6050_App_IsCalibrated(void);
uint8_t MPU6050_App_IsConnected(void);

/* 工具函数 */
void MPU6050_App_PrintData(void);
void MPU6050_App_PrintAttitude(void);
void MPU6050_App_Reset(void);

#endif
