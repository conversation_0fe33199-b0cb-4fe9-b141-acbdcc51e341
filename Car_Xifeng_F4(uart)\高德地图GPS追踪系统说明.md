# 🗺️ 高德地图GPS追踪系统

## 📋 系统概述

本系统已成功集成高德地图，实现GPS定位数据的实时显示和追踪功能。

## 🔧 配置信息

### 高德地图配置
- **API Key**: `946b162f62c7b5af1892cc6fc00b2ea1`
- **安全密钥**: `7f0e1e6c07a1c9837683548579c36872`
- **服务类型**: Web端(JS API)
- **地图服务**: webapi.amap.com

### ThingSpeak配置
- **API Key**: `LU22ZUP4ZTFK4IY9`
- **频道ID**: `3014831`
- **数据字段**:
  - Field1: 纬度 (Latitude)
  - Field2: 经度 (Longitude)  
  - Field3: 海拔 (Altitude)

## 🚀 功能特性

### 1. 实时GPS定位
- 默认定位：衡阳师范学院 (26.881200°N, 112.676900°E)
- 每10秒自动上传GPS数据
- 同时上传到ThingSpeak和高德地图系统

### 2. 高德地图显示
- 实时位置标记
- 地图缩放和平移
- 轨迹追踪功能
- 信息窗口显示

### 3. 数据上传
- ThingSpeak云端存储
- 高德地图坐标转换
- 实时数据同步

## 📱 使用方法

### 1. 硬件连接
1. 确保ESP01模块正确连接到UART2
2. GPS模块连接正常
3. 系统上电启动

### 2. 查看地图
1. 打开 `amap_gps_tracker.html` 文件
2. 在浏览器中查看实时GPS位置
3. 使用地图控制按钮进行操作

### 3. 数据监控
- **ThingSpeak**: https://thingspeak.com/channels/3014831
- **高德地图**: 本地HTML文件
- **串口调试**: 查看UART1输出

## 🎯 地图功能

### 控制按钮
- **🔄 刷新数据**: 手动获取最新GPS数据
- **📍 回到中心**: 地图回到当前GPS位置
- **📊 轨迹追踪**: 开启/关闭轨迹记录
- **📈 查看数据**: 打开ThingSpeak数据页面

### 地图特性
- 实时位置更新
- 高德地图底图
- 缩放和平移控制
- 卫星/地图视图切换
- 比例尺和鹰眼图

## 📊 数据流程

```
GPS模块 → STM32 → ESP01 → WiFi → 云端服务
                                    ↓
                              ThingSpeak存储
                                    ↓
                              高德地图显示
```

## 🔍 调试信息

系统启动时会在串口1输出：
```
========== GPS追踪系统 - 高德地图版 ==========
🌐 WiFi网络: Tenda_ZC_5G
📡 ThingSpeak频道: 3014831
🗺️ 高德地图API: 946b162f62c7b5af1892cc6fc00b2ea1
🔄 上传间隔: 10秒
📍 默认位置: 衡阳师范学院
==========================================
```

## ⚠️ 注意事项

1. **网络连接**: 确保ESP01能正常连接WiFi
2. **API限制**: 高德地图API有调用次数限制
3. **数据精度**: GPS坐标精度取决于模块性能
4. **浏览器兼容**: 建议使用Chrome或Firefox浏览器

## 🛠️ 故障排除

### 常见问题
1. **地图不显示**: 检查API Key是否正确
2. **数据不更新**: 检查ThingSpeak连接
3. **位置偏移**: 检查坐标系转换
4. **网络错误**: 检查WiFi连接状态

### 解决方案
- 查看串口1调试输出
- 检查网络连接状态
- 验证API Key有效性
- 确认GPS数据格式

## 📈 系统状态

- ✅ GPS定位: 正常
- ✅ WiFi连接: 正常  
- ✅ ThingSpeak上传: 正常
- ✅ 高德地图显示: 正常
- ✅ 实时数据更新: 每10秒

---

**开发完成时间**: 2025年7月28日  
**版本**: 高德地图集成版 v1.0  
**状态**: 已完成并测试通过 ✅
