#!/usr/bin/env python3
"""
网络连接检查工具
用于验证ESP01可以连接的服务器和端口

使用方法:
python network_check.py
"""

import socket
import requests
import time
import subprocess
import platform

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本地IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def check_port(host, port, timeout=5):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def check_http_service(url, timeout=10):
    """检查HTTP服务是否可访问"""
    try:
        response = requests.get(url, timeout=timeout)
        return response.status_code == 200
    except:
        return False

def ping_host(host):
    """Ping主机检查连通性"""
    try:
        if platform.system().lower() == "windows":
            cmd = ["ping", "-n", "1", host]
        else:
            cmd = ["ping", "-c", "1", host]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except:
        return False

def main():
    print("🔍 ESP01网络连接检查工具")
    print("=" * 50)
    
    # 获取本机IP
    local_ip = get_local_ip()
    print(f"🖥️ 本机IP地址: {local_ip}")
    print()
    
    # 检查的服务器列表
    servers = [
        {"name": "HTTPBin", "host": "httpbin.org", "port": 80, "url": "http://httpbin.org/get"},
        {"name": "百度", "host": "www.baidu.com", "port": 80, "url": "http://www.baidu.com"},
        {"name": "腾讯", "host": "www.qq.com", "port": 80, "url": "http://www.qq.com"},
        {"name": "本地服务器", "host": local_ip, "port": 8080, "url": f"http://{local_ip}:8080"}
    ]
    
    print("📡 检查服务器连接状态:")
    print("-" * 50)
    
    for server in servers:
        print(f"🌐 {server['name']} ({server['host']}:{server['port']})")
        
        # Ping测试
        ping_ok = ping_host(server['host'])
        print(f"   📶 Ping: {'✅ 成功' if ping_ok else '❌ 失败'}")
        
        # 端口测试
        port_ok = check_port(server['host'], server['port'])
        print(f"   🔌 端口: {'✅ 开放' if port_ok else '❌ 关闭'}")
        
        # HTTP测试
        if server['name'] != "本地服务器":  # 跳过本地服务器的HTTP测试
            http_ok = check_http_service(server['url'])
            print(f"   🌐 HTTP: {'✅ 可访问' if http_ok else '❌ 不可访问'}")
        
        print()
    
    # 检查本地端口占用
    print("🔍 检查本地端口状态:")
    print("-" * 30)
    
    local_ports = [8080, 8800, 3000, 5000]
    for port in local_ports:
        is_open = check_port('localhost', port)
        print(f"   端口 {port}: {'🟢 已占用' if is_open else '🔴 空闲'}")
    
    print()
    
    # 提供建议
    print("💡 建议:")
    print("-" * 20)
    print("1. 如果HTTPBin可访问，ESP01应该能正常连接外网")
    print("2. 如果本地8080端口空闲，可以运行测试服务器:")
    print("   python esp01_test_server.py")
    print("3. ESP01连接地址应该设置为:")
    print(f"   {local_ip}:8080")
    print("4. 确保防火墙允许8080端口的入站连接")
    
    # 生成ESP01配置建议
    print()
    print("⚙️ ESP01配置建议:")
    print("-" * 30)
    print("在esp01_app.c中更新服务器配置:")
    print(f'   {{"本地服务器", "{local_ip}", "{local_ip}", 8080}}')
    
if __name__ == "__main__":
    main()
