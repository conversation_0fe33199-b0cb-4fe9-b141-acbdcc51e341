<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航数据调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .data-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ GPS导航数据调试工具</h1>
        
        <div class="status info">
            <strong>ThingSpeak频道:</strong> 3014831<br>
            <strong>检查间隔:</strong> 5秒
        </div>

        <button onclick="checkData()">🔄 立即检查数据</button>
        <button onclick="toggleAutoRefresh()">⏸️ 暂停自动刷新</button>
        
        <h2>📊 最新数据</h2>
        <div id="status" class="status info">正在获取数据...</div>
        
        <h3>GPS数据 (field1, field2, field3)</h3>
        <div id="gpsData" class="data-box">等待数据...</div>
        
        <h3>导航数据 (field4)</h3>
        <div id="navigationData" class="data-box">等待数据...</div>
        
        <h3>完整响应</h3>
        <div id="fullResponse" class="data-box">等待数据...</div>
        
        <h2>📈 数据历史</h2>
        <div id="dataHistory" class="data-box">等待数据...</div>
    </div>

    <script>
        let autoRefresh = true;
        let refreshInterval;

        function checkData() {
            document.getElementById('status').textContent = '正在获取数据...';
            document.getElementById('status').className = 'status info';
            
            // 获取最新数据
            fetch('https://api.thingspeak.com/channels/3014831/feeds/last.json?api_key=LU22ZUP4ZTFK4IY9')
                .then(response => response.json())
                .then(data => {
                    console.log('ThingSpeak数据:', data);
                    
                    // 显示GPS数据
                    const gpsData = `纬度: ${data.field1 || '无数据'}
经度: ${data.field2 || '无数据'}
海拔: ${data.field3 || '无数据'}m
时间: ${data.created_at || '无数据'}`;
                    document.getElementById('gpsData').textContent = gpsData;
                    
                    // 显示导航数据
                    const navData = data.field4 || '无导航数据';
                    document.getElementById('navigationData').textContent = navData;
                    
                    // 显示完整响应
                    document.getElementById('fullResponse').textContent = JSON.stringify(data, null, 2);
                    
                    // 更新状态
                    if (data.field4) {
                        document.getElementById('status').textContent = '✅ 检测到导航数据！';
                        document.getElementById('status').className = 'status success';
                    } else {
                        document.getElementById('status').textContent = '⚠️ 未检测到导航数据 (field4为空)';
                        document.getElementById('status').className = 'status error';
                    }
                })
                .catch(error => {
                    console.error('获取数据失败:', error);
                    document.getElementById('status').textContent = '❌ 获取数据失败: ' + error.message;
                    document.getElementById('status').className = 'status error';
                });
            
            // 获取历史数据
            fetch('https://api.thingspeak.com/channels/3014831/feeds.json?api_key=LU22ZUP4ZTFK4IY9&results=5')
                .then(response => response.json())
                .then(data => {
                    let history = '最近5条记录:\n\n';
                    data.feeds.forEach((feed, index) => {
                        history += `${index + 1}. 时间: ${feed.created_at}\n`;
                        history += `   GPS: ${feed.field1}, ${feed.field2}, ${feed.field3}m\n`;
                        history += `   导航: ${feed.field4 || '无'}\n\n`;
                    });
                    document.getElementById('dataHistory').textContent = history;
                })
                .catch(error => {
                    document.getElementById('dataHistory').textContent = '获取历史数据失败: ' + error.message;
                });
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const button = document.querySelector('button[onclick="toggleAutoRefresh()"]');
            
            if (autoRefresh) {
                button.textContent = '⏸️ 暂停自动刷新';
                startAutoRefresh();
            } else {
                button.textContent = '▶️ 开始自动刷新';
                clearInterval(refreshInterval);
            }
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(checkData, 5000);
        }

        // 页面加载时开始
        window.onload = function() {
            checkData();
            startAutoRefresh();
        };
    </script>
</body>
</html>
