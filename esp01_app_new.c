/**
 * ESP01 WiFi模块应用程序 - 全新版本
 * 
 * 功能：
 * 1. ESP-01初始化和WiFi连接
 * 2. GPS数据上传到国内服务器
 * 3. 支持多种国内服务器连接
 * 4. 简化连接逻辑，提高成功率
 * 
 * Author: AI Assistant
 * Date: 2025-01-27
 */

#include "esp01_app.h"
#include "GPS_app.h"
#include "stdlib.h"

extern uint8_t uart2_rx_dma_buffer[];
extern DMA_HandleTypeDef hdma_usart2_rx;

#define UART2_BUFFER_SIZE 512
static uint8_t uart2_buffer[UART2_BUFFER_SIZE];
static volatile uint16_t uart2_rx_len = 0;

// ==================== WiFi配置 ====================
#define WIFI_SSID "Tenda_ZC_5G"
#define WIFI_PASSWORD "zhongchuang"

// ==================== 国内服务器配置 ====================
#define SERVER_HOST "www.baidu.com"        // 百度服务器
#define SERVER_IP "*************"          // 百度IP
#define SERVER_PORT 80

// 备用服务器
#define BACKUP_HOST "www.qq.com"           // 腾讯服务器
#define BACKUP_IP "************"           // 腾讯IP

// ==================== 状态管理 ====================
static ESP01_State_t esp01_state = ESP01_STATE_IDLE;
static volatile uint8_t wifi_connected = 0;
static volatile uint8_t tcp_connected = 0;
static uint32_t last_upload_time = 0;

// ==================== 数据缓冲区 ====================
static char http_buffer[512];
static char gps_data_buffer[128];

/**
 * ESP01初始化
 */
void esp01_Init(void)
{
    esp01_state = ESP01_STATE_IDLE;
    wifi_connected = 0;
    tcp_connected = 0;
    last_upload_time = 0;
    
    my_printf(&huart1, "\r\n========== ESP01 新系统启动 ==========\r\n");
    my_printf(&huart1, "🌐 目标网络: %s\r\n", WIFI_SSID);
    my_printf(&huart1, "🖥️ 服务器: %s\r\n", SERVER_HOST);
    my_printf(&huart1, "🔄 上传间隔: 10秒\r\n");
    my_printf(&huart1, "=====================================\r\n\r\n");
    
    // 启动初始化序列
    esp01_InitSequence();
}

/**
 * ESP01初始化序列
 */
void esp01_InitSequence(void)
{
    my_printf(&huart1, "🚀 开始ESP01初始化...\r\n");
    esp01_state = ESP01_STATE_INIT;
    
    // 1. 测试AT命令
    my_printf(&huart1, "📡 测试AT命令...\r\n");
    Uart2_Printf(&huart2, "AT\r\n");
    HAL_Delay(1000);
    
    // 2. 设置WiFi模式
    my_printf(&huart1, "⚙️ 设置WiFi模式...\r\n");
    Uart2_Printf(&huart2, "AT+CWMODE=1\r\n");
    HAL_Delay(1000);
    
    // 3. 连接WiFi
    my_printf(&huart1, "🔗 连接WiFi网络...\r\n");
    Uart2_Printf(&huart2, "AT+CWJAP=\"%s\",\"%s\"\r\n", WIFI_SSID, WIFI_PASSWORD);
    HAL_Delay(8000);  // WiFi连接需要更长时间
    
    wifi_connected = 1;
    esp01_state = ESP01_STATE_CONNECTED;
    my_printf(&huart1, "✅ ESP01初始化完成\r\n");
}

/**
 * 测试网络连接
 */
uint8_t esp01_TestConnection(void)
{
    my_printf(&huart1, "🔍 测试网络连接...\r\n");
    
    // 测试连接百度
    my_printf(&huart1, "📡 连接百度服务器...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", SERVER_HOST, SERVER_PORT);
    HAL_Delay(5000);
    
    // 发送简单HTTP请求
    char test_request[] = "GET / HTTP/1.1\r\nHost: www.baidu.com\r\nConnection: close\r\n\r\n";
    int request_len = strlen(test_request);
    
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", request_len);
    HAL_Delay(1000);
    Uart2_Printf(&huart2, "%s", test_request);
    HAL_Delay(3000);
    
    // 关闭连接
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);
    
    my_printf(&huart1, "✅ 网络连接测试完成\r\n");
    return 1;
}

/**
 * 上传GPS数据
 */
uint8_t esp01_UploadGPS(void)
{
    if (!wifi_connected) {
        my_printf(&huart1, "❌ WiFi未连接\r\n");
        return 0;
    }
    
    my_printf(&huart1, "📤 开始GPS数据上传...\r\n");
    
    // 获取GPS数据
    float lat, lon, alt;
    esp01_GetRealLocation(&lat, &lon, &alt);
    
    // 连接服务器
    my_printf(&huart1, "🔗 连接服务器...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", SERVER_HOST, SERVER_PORT);
    HAL_Delay(5000);
    
    // 构建HTTP请求（发送到httpbin.org进行测试）
    snprintf(http_buffer, sizeof(http_buffer),
             "GET /get?lat=%.6f&lon=%.6f&alt=%.1f&device=ESP01_GPS HTTP/1.1\r\n"
             "Host: httpbin.org\r\n"
             "User-Agent: ESP01-GPS-Tracker\r\n"
             "Connection: close\r\n\r\n",
             lat, lon, alt);
    
    int data_len = strlen(http_buffer);
    
    // 发送数据
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", data_len);
    HAL_Delay(1000);
    Uart2_Printf(&huart2, "%s", http_buffer);
    HAL_Delay(3000);
    
    // 关闭连接
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);
    
    last_upload_time = HAL_GetTick();
    
    my_printf(&huart1, "✅ GPS数据上传完成\r\n");
    my_printf(&huart1, "📍 位置: %.6fN, %.6fE, %.1fm\r\n", lat, lon, alt);
    my_printf(&huart1, "⏰ 下次上传: 10秒后\r\n\r\n");
    
    return 1;
}

/**
 * 简化的GPS数据上传（使用GET请求）
 */
uint8_t esp01_SimpleUpload(void)
{
    my_printf(&huart1, "🌐 简化GPS上传...\r\n");
    
    // 获取GPS数据
    float lat, lon, alt;
    esp01_GetRealLocation(&lat, &lon, &alt);
    
    // 连接到httpbin.org（国外但通常可访问）
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"httpbin.org\",80\r\n");
    HAL_Delay(5000);
    
    // 构建简单GET请求
    snprintf(http_buffer, sizeof(http_buffer),
             "GET /get?gps=%.6f,%.6f,%.1f HTTP/1.1\r\n"
             "Host: httpbin.org\r\n"
             "Connection: close\r\n\r\n",
             lat, lon, alt);
    
    int data_len = strlen(http_buffer);
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", data_len);
    HAL_Delay(1000);
    Uart2_Printf(&huart2, "%s", http_buffer);
    HAL_Delay(3000);
    
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);
    
    my_printf(&huart1, "✅ 简化上传完成\r\n");
    return 1;
}

/**
 * ESP01主任务
 */
void esp01_Task(void)
{
    static uint32_t last_task_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每10秒执行一次GPS上传
    if (current_time - last_task_time >= 10000) {
        last_task_time = current_time;
        
        if (esp01_state == ESP01_STATE_CONNECTED) {
            esp01_UploadGPS();
        }
    }
}

/**
 * 获取ESP01状态
 */
ESP01_State_t esp01_GetState(void)
{
    return esp01_state;
}

/**
 * 检查连接状态
 */
void esp01_CheckConnection(void)
{
    my_printf(&huart1, "🔍 检查连接状态...\r\n");
    
    if (wifi_connected) {
        my_printf(&huart1, "✅ WiFi: 已连接\r\n");
    } else {
        my_printf(&huart1, "❌ WiFi: 未连接\r\n");
    }
    
    my_printf(&huart1, "📊 系统状态: %s\r\n", 
              esp01_state == ESP01_STATE_CONNECTED ? "正常" : "异常");
}

/**
 * 获取真实GPS位置（模拟数据）
 */
void esp01_GetRealLocation(float *lat, float *lon, float *alt)
{
    // 使用模拟的GPS数据（长沙地区）
    *lat = 28.2282 + (rand() % 1000) * 0.0001;  // 长沙纬度附近
    *lon = 112.9388 + (rand() % 1000) * 0.0001; // 长沙经度附近
    *alt = 50.0 + (rand() % 100);               // 海拔50-150米
}
