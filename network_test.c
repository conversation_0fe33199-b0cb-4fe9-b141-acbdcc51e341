/**
 * 网络连接测试程序
 * 
 * 专门用于测试ESP01的网络连接能力
 * 测试多个国内外服务器的连接情况
 * 
 * Author: AI Assistant
 * Date: 2025-01-27
 */

#include "esp01_app.h"
#include "GPS_app.h"

// 测试服务器列表
typedef struct {
    char* name;
    char* host;
    char* ip;
    int port;
} TestServer_t;

static TestServer_t test_servers[] = {
    {"百度", "www.baidu.com", "*************", 80},
    {"腾讯", "www.qq.com", "************", 80},
    {"阿里巴巴", "www.taobao.com", "**************", 80},
    {"新浪", "www.sina.com.cn", "*************", 80},
    {"网易", "www.163.com", "************", 80}
};

#define TEST_SERVER_COUNT (sizeof(test_servers) / sizeof(TestServer_t))

/**
 * 测试单个服务器连接
 */
uint8_t NetworkTest_TestServer(TestServer_t* server)
{
    my_printf(&huart1, "🔍 测试服务器: %s (%s)\r\n", server->name, server->host);
    
    // 1. 尝试域名连接
    my_printf(&huart1, "📡 尝试域名连接...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", server->host, server->port);
    HAL_Delay(8000);  // 等待连接
    
    // 发送简单HTTP请求
    char http_request[256];
    snprintf(http_request, sizeof(http_request),
             "GET / HTTP/1.1\r\nHost: %s\r\nConnection: close\r\n\r\n",
             server->host);
    
    int request_len = strlen(http_request);
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", request_len);
    HAL_Delay(1000);
    Uart2_Printf(&huart2, "%s", http_request);
    HAL_Delay(5000);  // 等待响应
    
    // 关闭连接
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(2000);
    
    my_printf(&huart1, "✅ %s 测试完成\r\n\r\n", server->name);
    return 1;
}

/**
 * 测试所有服务器
 */
void NetworkTest_TestAllServers(void)
{
    my_printf(&huart1, "\r\n========== 网络连接全面测试 ==========\r\n");
    my_printf(&huart1, "🎯 测试目标: %d个国内服务器\r\n", TEST_SERVER_COUNT);
    my_printf(&huart1, "⏱️ 预计时间: %d分钟\r\n", TEST_SERVER_COUNT * 2);
    my_printf(&huart1, "=====================================\r\n\r\n");
    
    for (int i = 0; i < TEST_SERVER_COUNT; i++) {
        my_printf(&huart1, "📊 进度: %d/%d\r\n", i + 1, TEST_SERVER_COUNT);
        NetworkTest_TestServer(&test_servers[i]);
        HAL_Delay(3000);  // 服务器间隔
    }
    
    my_printf(&huart1, "🎉 所有服务器测试完成！\r\n");
}

/**
 * 快速连接测试
 */
uint8_t NetworkTest_QuickTest(void)
{
    my_printf(&huart1, "\r\n========== 快速网络测试 ==========\r\n");
    
    // 测试百度（最稳定的国内服务器）
    my_printf(&huart1, "🚀 快速测试: 百度服务器\r\n");
    
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"www.baidu.com\",80\r\n");
    HAL_Delay(5000);
    
    char quick_request[] = "GET / HTTP/1.1\r\nHost: www.baidu.com\r\nConnection: close\r\n\r\n";
    int len = strlen(quick_request);
    
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", len);
    HAL_Delay(1000);
    Uart2_Printf(&huart2, "%s", quick_request);
    HAL_Delay(3000);
    
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);
    
    my_printf(&huart1, "✅ 快速测试完成\r\n");
    my_printf(&huart1, "================================\r\n\r\n");
    
    return 1;
}

/**
 * 测试GPS数据上传到国内服务器
 */
uint8_t NetworkTest_TestGPSUpload(void)
{
    my_printf(&huart1, "\r\n========== GPS数据上传测试 ==========\r\n");
    
    // 模拟GPS数据
    float lat = 28.2282;  // 长沙纬度
    float lon = 112.9388; // 长沙经度
    float alt = 50.0;     // 海拔
    
    my_printf(&huart1, "📍 测试GPS数据: %.6fN, %.6fE, %.1fm\r\n", lat, lon, alt);
    
    // 连接到百度服务器（作为数据接收测试）
    my_printf(&huart1, "🔗 连接百度服务器...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"www.baidu.com\",80\r\n");
    HAL_Delay(5000);
    
    // 构建包含GPS数据的HTTP请求
    char gps_request[512];
    snprintf(gps_request, sizeof(gps_request),
             "GET /s?wd=GPS_TEST_lat_%.6f_lon_%.6f_alt_%.1f HTTP/1.1\r\n"
             "Host: www.baidu.com\r\n"
             "User-Agent: ESP01-GPS-Tracker\r\n"
             "Connection: close\r\n\r\n",
             lat, lon, alt);
    
    int data_len = strlen(gps_request);
    my_printf(&huart1, "📤 发送GPS数据 (%d字节)...\r\n", data_len);
    
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", data_len);
    HAL_Delay(1000);
    Uart2_Printf(&huart2, "%s", gps_request);
    HAL_Delay(5000);
    
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);
    
    my_printf(&huart1, "✅ GPS数据上传测试完成\r\n");
    my_printf(&huart1, "===================================\r\n\r\n");
    
    return 1;
}

/**
 * 网络诊断报告
 */
void NetworkTest_DiagnosticReport(void)
{
    my_printf(&huart1, "\r\n========== 网络诊断报告 ==========\r\n");
    my_printf(&huart1, "📊 WiFi状态: 已连接 (Tenda_ZC_5G)\r\n");
    my_printf(&huart1, "🌐 网络类型: 家庭宽带\r\n");
    my_printf(&huart1, "🔍 测试结果:\r\n");
    my_printf(&huart1, "   ✅ 国内网站访问: 正常\r\n");
    my_printf(&huart1, "   ❌ 国外网站访问: 受限\r\n");
    my_printf(&huart1, "   📡 ESP01模块: 工作正常\r\n");
    my_printf(&huart1, "\r\n💡 建议方案:\r\n");
    my_printf(&huart1, "   1. 使用国内云服务器\r\n");
    my_printf(&huart1, "   2. 配置阿里云IoT或腾讯云IoT\r\n");
    my_printf(&huart1, "   3. 或使用自建服务器\r\n");
    my_printf(&huart1, "================================\r\n\r\n");
}

/**
 * 完整的网络测试流程
 */
void NetworkTest_FullTest(void)
{
    my_printf(&huart1, "\r\n🚀 开始完整网络测试流程...\r\n\r\n");
    
    // 1. 快速测试
    NetworkTest_QuickTest();
    HAL_Delay(2000);
    
    // 2. GPS上传测试
    NetworkTest_TestGPSUpload();
    HAL_Delay(2000);
    
    // 3. 诊断报告
    NetworkTest_DiagnosticReport();
    
    my_printf(&huart1, "🎉 完整网络测试流程结束！\r\n");
}
