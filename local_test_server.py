#!/usr/bin/env python3
"""
本地GPS测试服务器
用于在局域网内测试ESP01的GPS数据上传功能

运行方法：
python local_test_server.py

然后修改ESP01代码连接到这个服务器
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime
import threading
import time

class GPSTestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        try:
            # 解析URL和参数
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            
            print(f"\n{'='*50}")
            print(f"🕒 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📡 收到ESP01请求: {self.path}")
            print(f"🌐 客户端IP: {self.client_address[0]}")
            
            # 检查是否是GPS数据
            if 'api_key' in query_params:
                self.handle_gps_data(query_params)
            else:
                self.handle_test_request(parsed_url.path)
            
            # 返回成功响应
            response_data = {
                "success": True,
                "message": "GPS数据接收成功",
                "timestamp": datetime.now().isoformat(),
                "server": "Local GPS Test Server"
            }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            print(f"❌ 处理请求时出错: {e}")
            self.send_error(500, f"服务器错误: {e}")
    
    def handle_gps_data(self, params):
        """处理GPS数据"""
        print(f"📍 GPS数据解析:")
        
        # 提取GPS参数
        api_key = params.get('api_key', [''])[0]
        field1 = params.get('field1', [''])[0]  # 纬度
        field2 = params.get('field2', [''])[0]  # 经度
        field3 = params.get('field3', [''])[0]  # 海拔
        channel = params.get('channel', [''])[0]
        
        print(f"   🔑 API Key: {api_key}")
        print(f"   📊 Channel: {channel}")
        print(f"   🌍 纬度: {field1}")
        print(f"   🌍 经度: {field2}")
        print(f"   ⛰️ 海拔: {field3}m")
        
        # 验证GPS数据
        try:
            lat = float(field1) if field1 else 0
            lon = float(field2) if field2 else 0
            alt = float(field3) if field3 else 0
            
            if 20 <= lat <= 50 and 100 <= lon <= 130:
                print(f"   ✅ GPS坐标有效 (中国境内)")
            else:
                print(f"   ⚠️ GPS坐标可能异常")
                
        except ValueError:
            print(f"   ❌ GPS数据格式错误")
        
        # 保存到文件
        self.save_gps_data(api_key, field1, field2, field3, channel)
    
    def handle_test_request(self, path):
        """处理测试请求"""
        print(f"🔍 测试请求: {path}")
        if path == '/':
            print(f"   ✅ 根路径访问 - 连接测试成功")
        else:
            print(f"   📝 其他路径访问")
    
    def save_gps_data(self, api_key, lat, lon, alt, channel):
        """保存GPS数据到文件"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            gps_record = f"{timestamp},{api_key},{channel},{lat},{lon},{alt}\n"
            
            with open('gps_data.csv', 'a', encoding='utf-8') as f:
                f.write(gps_record)
            
            print(f"   💾 数据已保存到 gps_data.csv")
            
        except Exception as e:
            print(f"   ❌ 保存数据失败: {e}")
    
    def log_message(self, format, *args):
        """禁用默认的访问日志"""
        pass

def get_local_ip():
    """获取本机IP地址"""
    import socket
    try:
        # 连接到一个不存在的地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        # 如果自动获取失败，返回您的实际IP
        return "**************"

def print_server_info(ip, port):
    """打印服务器信息"""
    print(f"""
{'='*60}
🌐 本地GPS测试服务器已启动
{'='*60}
📡 服务器地址: http://{ip}:{port}
🔗 测试URL: http://{ip}:{port}/
📍 GPS上传URL: http://{ip}:{port}/gps

📋 使用说明:
1. 确保ESP01和电脑在同一局域网
2. 修改ESP01代码中的服务器地址为: {ip}
3. ESP01将GPS数据发送到这个服务器
4. 查看控制台输出和gps_data.csv文件

⏹️ 按 Ctrl+C 停止服务器
{'='*60}
""")

def main():
    """主函数"""
    HOST = '0.0.0.0'  # 监听所有网络接口
    PORT = 8080
    
    # 获取本机IP
    local_ip = get_local_ip()
    
    # 创建服务器
    server = HTTPServer((HOST, PORT), GPSTestHandler)
    
    # 打印服务器信息
    print_server_info(local_ip, PORT)
    
    # 创建CSV文件头
    try:
        with open('gps_data.csv', 'w', encoding='utf-8') as f:
            f.write('时间,API_Key,Channel,纬度,经度,海拔\n')
    except:
        pass
    
    try:
        # 启动服务器
        server.serve_forever()
    except KeyboardInterrupt:
        print(f"\n\n🛑 服务器已停止")
        server.shutdown()

if __name__ == '__main__':
    main()
