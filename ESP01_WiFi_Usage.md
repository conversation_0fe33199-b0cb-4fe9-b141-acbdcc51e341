# ESP-01 WiFi连接功能使用说明

## 概述
本项目已为ESP-01模块添加了完整的WiFi连接功能，使用串口1进行通信。

## 配置信息
- **WiFi名称**: Tenda_ZC_5G
- **WiFi密码**: zhongchuang
- **ESP-01通信串口**: UART2 (115200波特率)
- **ESP-01连接引脚**: PA2(TX), PA3(RX)
- **调试输出串口**: UART1 (115200波特率)

## 功能特性

### 1. 自动初始化序列
系统启动后，ESP-01会自动执行以下初始化步骤：
1. 发送AT测试指令
2. 重启ESP-01模块
3. 设置WiFi模式为Station模式
4. 连接到指定的WiFi网络

### 2. 状态管理
ESP-01模块具有以下状态：
- **IDLE**: 空闲状态
- **INIT**: 初始化中
- **CONNECTING**: 连接WiFi中
- **CONNECTED**: 已连接WiFi
- **ERROR**: 错误状态

### 3. 自动重试机制
- 连接失败时自动重试，最多重试3次
- 每次重试间隔5秒
- 连接成功后每30秒检查一次连接状态

## 串口命令

通过串口1可以发送以下命令来控制和查询ESP-01状态：

### 查询状态
发送: `esp_status`
返回: ESP-01当前状态信息

### 重置模块
发送: `esp_reset`
功能: 重新初始化ESP-01模块

### 原有测试命令
发送: `hello`
功能: 测试串口通信并切换LED状态

## 自动响应处理

系统会自动识别ESP-01返回的以下响应：
- `OK`: 命令执行成功
- `WIFI CONNECTED`: WiFi连接成功
- `WIFI GOT IP`: 获取IP地址成功
- `ERROR`: 命令执行错误
- `FAIL`: 命令执行失败

## 使用方法

### 1. 硬件连接
确保ESP-01模块正确连接到STM32的UART2：
- ESP-01 RX -> STM32 PA2 (UART2_TX)
- ESP-01 TX -> STM32 PA3 (UART2_RX)
- ESP-01 VCC -> 3.3V
- ESP-01 GND -> GND

同时确保串口1连接到调试工具：
- STM32 PA9 (UART1_TX) -> USB转串口模块RX
- STM32 PA10 (UART1_RX) -> USB转串口模块TX

### 2. 软件集成
代码已自动集成到调度器中，无需额外配置。系统启动后会自动：
- 初始化ESP-01模块
- 每100ms执行一次ESP-01任务
- 处理串口接收的ESP-01响应

### 3. 监控连接状态
通过串口1调试工具可以实时查看：
- ESP-01初始化过程和AT指令发送
- ESP-01响应数据（包括原始数据和解析结果）
- WiFi连接状态变化
- 错误信息和重试过程

**工作原理**：
- AT指令通过串口2发送给ESP-01
- ESP-01响应通过串口2接收
- 所有调试信息通过串口1输出到PC

## 代码结构

### 主要文件
- `esp01_app.c`: ESP-01应用层实现
- `esp01_app.h`: ESP-01应用层头文件
- `uart_app.c`: 串口应用层（包含ESP-01响应处理）
- `scheduler.c`: 任务调度器（包含ESP-01任务）

### 主要函数
- `esp01_Init()`: 初始化ESP-01
- `esp01_Task()`: ESP-01主任务（状态机处理）
- `esp01_GetState()`: 获取当前状态
- `esp01_SetConnected()`: 设置连接成功状态
- `esp01_Reset()`: 重置模块
- `esp01_SendCommand()`: 发送自定义AT指令

## 调试信息

系统会通过串口1输出详细的调试信息，包括：
- 初始化步骤进度
- AT指令发送记录
- WiFi连接状态变化
- 错误信息和重试次数

## 注意事项

1. **电源要求**: ESP-01需要稳定的3.3V电源，电流至少200mA
2. **波特率匹配**: 确保ESP-01的波特率设置为115200
3. **天线连接**: 确保ESP-01天线连接良好以获得最佳信号
4. **WiFi信号**: 确保设备在WiFi信号覆盖范围内
5. **串口冲突**: 如果需要同时使用串口调试，注意避免与ESP-01通信冲突

## 故障排除

### 连接失败
1. 检查WiFi名称和密码是否正确
2. 确认WiFi信号强度
3. 检查ESP-01硬件连接
4. 查看串口输出的错误信息

### 模块无响应
1. 检查电源供应是否稳定
2. 确认串口连接是否正确
3. 尝试发送`esp_reset`命令重置模块
4. 检查ESP-01模块是否损坏

### 频繁断线
1. 检查电源稳定性
2. 确认WiFi信号强度
3. 检查天线连接
4. 考虑增加连接检查频率
