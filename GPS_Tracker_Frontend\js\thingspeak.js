// ThingSpeak API 交互类
class ThingSpeakAPI {
    constructor() {
        this.baseUrl = CONFIG.THINGSPEAK.BASE_URL;
        this.channelId = CONFIG.THINGSPEAK.CHANNEL_ID;
        this.readApiKey = CONFIG.THINGSPEAK.READ_API_KEY;
        this.writeApiKey = CONFIG.THINGSPEAK.WRITE_API_KEY;
    }

    // 获取最新的GPS数据
    async getLatestData() {
        try {
            const url = `${this.baseUrl}/channels/${this.channelId}/feeds/last.json?api_key=${this.readApiKey}`;
            
            log(`正在获取最新数据: ${url}`);
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data || !data.created_at) {
                throw new Error('没有可用数据');
            }
            
            // 解析GPS数据
            const gpsData = this.parseGPSData(data);
            log('获取到GPS数据:', 'success');
            log(gpsData);
            
            return gpsData;
            
        } catch (error) {
            log(`获取数据失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 获取历史数据(用于轨迹显示)
    async getHistoryData(results = 20) {
        try {
            const url = `${this.baseUrl}/channels/${this.channelId}/feeds.json?api_key=${this.readApiKey}&results=${results}`;
            
            log(`正在获取历史数据: ${results}条记录`);
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.feeds || data.feeds.length === 0) {
                log('没有历史数据', 'warn');
                return [];
            }
            
            // 解析所有GPS数据点
            const trackData = data.feeds
                .map(feed => this.parseGPSData(feed))
                .filter(point => point.latitude && point.longitude); // 过滤无效数据
            
            log(`获取到${trackData.length}个有效轨迹点`, 'success');
            return trackData;
            
        } catch (error) {
            log(`获取历史数据失败: ${error.message}`, 'error');
            return [];
        }
    }

    // 解析GPS数据
    parseGPSData(feed) {
        const fields = CONFIG.THINGSPEAK.FIELDS;
        
        return {
            timestamp: new Date(feed.created_at),
            latitude: parseFloat(feed[fields.LATITUDE]) || null,
            longitude: parseFloat(feed[fields.LONGITUDE]) || null,
            altitude: parseFloat(feed[fields.ALTITUDE]) || null,
            entry_id: feed.entry_id || null,
            raw: feed
        };
    }

    // 发送GPS数据到ThingSpeak (如果需要从前端发送)
    async sendGPSData(latitude, longitude, altitude = null) {
        try {
            const fields = CONFIG.THINGSPEAK.FIELDS;
            const url = `${this.baseUrl}/update.json`;
            
            const data = {
                api_key: this.writeApiKey,
                [fields.LATITUDE]: latitude,
                [fields.LONGITUDE]: longitude
            };
            
            if (altitude !== null) {
                data[fields.ALTITUDE] = altitude;
            }
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const result = await response.json();
            log(`数据发送成功, Entry ID: ${result}`, 'success');
            return result;
            
        } catch (error) {
            log(`发送数据失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 获取频道信息
    async getChannelInfo() {
        try {
            const url = `${this.baseUrl}/channels/${this.channelId}.json?api_key=${this.readApiKey}`;
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            log('频道信息获取成功', 'success');
            return data;
            
        } catch (error) {
            log(`获取频道信息失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 检查API连接状态和数据新鲜度
    async checkConnection() {
        try {
            // 首先检查API是否可访问
            await this.getChannelInfo();

            // 然后检查最新数据的时间戳
            const latestData = await this.getLatestData();
            if (!latestData || !latestData.created_at) {
                log('没有找到有效的GPS数据', 'warning');
                return false;
            }

            // 检查数据新鲜度（5分钟内的数据认为是在线）
            const dataTime = new Date(latestData.created_at);
            const now = new Date();
            const timeDiff = (now - dataTime) / 1000 / 60; // 分钟差

            if (timeDiff > 5) {
                log(`数据过期：${timeDiff.toFixed(1)}分钟前的数据`, 'warning');
                return false;
            }

            log(`数据新鲜：${timeDiff.toFixed(1)}分钟前更新`, 'success');
            return true;

        } catch (error) {
            log(`连接检查失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 格式化ThingSpeak时间
    formatThingSpeakTime(timestamp) {
        return Utils.formatTime(new Date(timestamp));
    }
}

// 模拟数据生成器 (用于测试)
class MockDataGenerator {
    constructor() {
        this.baseLatitude = 26.88693;   // 衡阳师范学院纬度 (真正准确位置)
        this.baseLongitude = 112.675813; // 衡阳师范学院经度 (真正准确位置)
        this.baseAltitude = 50;         // 基础海拔
        this.currentIndex = 0;
    }

    // 生成模拟GPS数据
    generateMockData() {
        // 模拟在衡阳市区的移动轨迹
        const timeOffset = this.currentIndex * 30000; // 每30秒一个点
        const angle = (this.currentIndex * 0.1) % (2 * Math.PI); // 圆形移动
        const radius = 0.005; // 约500米半径

        const latitude = this.baseLatitude + Math.sin(angle) * radius;
        const longitude = this.baseLongitude + Math.cos(angle) * radius;
        const altitude = this.baseAltitude + Math.random() * 20 - 10; // ±10米变化

        this.currentIndex++;

        return {
            timestamp: new Date(Date.now() - timeOffset),
            latitude: parseFloat(latitude.toFixed(6)),
            longitude: parseFloat(longitude.toFixed(6)),
            altitude: parseFloat(altitude.toFixed(1)),
            entry_id: this.currentIndex,
            raw: {
                created_at: new Date(Date.now() - timeOffset).toISOString(),
                field1: latitude.toFixed(6),
                field2: longitude.toFixed(6),
                field3: altitude.toFixed(1)
            }
        };
    }

    // 生成历史轨迹数据
    generateHistoryData(count = 20) {
        const history = [];
        const originalIndex = this.currentIndex;
        this.currentIndex = 0;

        for (let i = 0; i < count; i++) {
            history.push(this.generateMockData());
        }

        this.currentIndex = originalIndex;
        return history.reverse(); // 按时间顺序排列
    }
}

// 创建全局实例
window.thingSpeakAPI = new ThingSpeakAPI();
window.mockDataGenerator = new MockDataGenerator();

// 导出类
window.ThingSpeakAPI = ThingSpeakAPI;
window.MockDataGenerator = MockDataGenerator;
