# 🚀 ThingSpeak配置和部署指南

## 📡 第一步：ThingSpeak配置

### 1. 创建ThingSpeak账号
1. 访问：https://thingspeak.com/
2. 点击 "Sign Up" 注册免费账号
3. 验证邮箱并登录

### 2. 创建Channel
1. 登录后点击 "Channels" → "My Channels"
2. 点击 "New Channel" 按钮
3. 填写Channel信息：
   ```
   Name: 衡阳GPS追踪
   Description: STM32 ESP-01 GPS tracking system for Hengyang
   Field 1: Latitude (纬度)
   Field 2: Longitude (经度)
   Field 3: Altitude (海拔)
   ```
4. 点击 "Save Channel"

### 3. 获取API密钥
1. 在您的Channel页面，点击 "API Keys" 标签
2. 复制以下信息：
   - **Channel ID** (数字，如：2747890)
   - **Read API Key** (16位字符串)
   - **Write API Key** (16位字符串)

### 4. 更新前端配置
编辑 `js/config.js` 文件，替换以下内容：
```javascript
THINGSPEAK: {
    CHANNEL_ID: '您的Channel ID',
    READ_API_KEY: '您的Read API Key',
    WRITE_API_KEY: '您的Write API Key',
    // ...
}
```

## 🔧 第二步：STM32端配置

确保您的STM32代码发送正确格式的数据：

### HTTP GET格式：
```c
// 在esp01_SendLocationData()函数中
sprintf(url, "/update?api_key=%s&field1=%.6f&field2=%.6f&field3=%.1f", 
        "您的Write API Key", latitude, longitude, altitude);
```

### 完整的HTTP请求示例：
```
GET /update?api_key=QRSTUVWXYZ123456&field1=26.896845&field2=112.570712&field3=47.3 HTTP/1.1
Host: api.thingspeak.com
Connection: close
```

## 🌐 第三步：部署到云端

### 方案1：GitHub Pages (推荐)
1. **创建GitHub仓库**：
   - 访问 https://github.com/
   - 点击 "New repository"
   - 仓库名：`gps-tracker-hengyang`
   - 设为Public

2. **上传文件**：
   - 将整个 `GPS_Tracker_Frontend` 文件夹内容上传
   - 或使用Git命令：
   ```bash
   git init
   git add .
   git commit -m "Initial GPS tracker"
   git remote add origin https://github.com/您的用户名/gps-tracker-hengyang.git
   git push -u origin main
   ```

3. **启用GitHub Pages**：
   - 在仓库设置中找到 "Pages"
   - Source选择 "Deploy from a branch"
   - Branch选择 "main"
   - 点击Save

4. **访问地址**：
   - `https://您的用户名.github.io/gps-tracker-hengyang/`

### 方案2：Netlify (简单拖拽)
1. 访问：https://www.netlify.com/
2. 注册免费账号
3. 点击 "Add new site" → "Deploy manually"
4. 直接拖拽 `GPS_Tracker_Frontend` 文件夹
5. 获得免费域名：`https://随机名称.netlify.app/`

### 方案3：Vercel (开发者友好)
1. 访问：https://vercel.com/
2. 用GitHub账号登录
3. 导入您的GitHub仓库
4. 自动部署，获得域名

## 📱 第四步：测试访问

### 本地测试
```bash
# 方法1：Python服务器
python -m http.server 8000
# 访问：http://localhost:8000

# 方法2：Node.js服务器
npx http-server
# 访问：http://localhost:8080
```

### 跨网络测试
部署成功后，在不同网络环境测试：
- 手机4G网络
- 家庭WiFi
- 公司网络
- 朋友家网络

## 🧪 第五步：功能验证

### 1. 模拟数据测试
- 打开网页，应该看到衡阳地图
- 如果ThingSpeak未配置，会自动显示模拟数据
- 观察红色标记在衡阳市区移动

### 2. 真实数据测试
- 配置好ThingSpeak后刷新页面
- STM32发送数据到ThingSpeak
- 网页应该显示真实GPS位置

### 3. 功能测试
- ✅ 地图显示正常
- ✅ 位置标记出现
- ✅ 轨迹线绘制
- ✅ 控制按钮工作
- ✅ 状态信息更新

## 🔍 故障排除

### 常见问题
1. **地图不显示**
   - 检查网络连接
   - 确认在HTTP服务器下运行（不是file://）

2. **数据不更新**
   - 检查ThingSpeak API密钥
   - 查看浏览器控制台错误信息
   - 确认STM32正在发送数据

3. **跨域错误**
   - 必须通过HTTP服务器访问
   - 不能直接双击HTML文件

### 调试技巧
1. 打开浏览器开发者工具 (F12)
2. 查看Console标签的日志信息
3. 查看Network标签的网络请求
4. 在config.js中设置 `DEBUG: true`

## 📞 获取帮助

### ThingSpeak相关
- 官方文档：https://www.mathworks.com/help/thingspeak/
- API参考：https://www.mathworks.com/help/thingspeak/rest-api.html

### 地图相关
- Leaflet文档：https://leafletjs.com/
- OpenStreetMap：https://www.openstreetmap.org/

### 部署相关
- GitHub Pages：https://pages.github.com/
- Netlify文档：https://docs.netlify.com/
- Vercel文档：https://vercel.com/docs

---

**🎯 完成这些步骤后，您就拥有了一个完整的跨网络GPS追踪系统！**
