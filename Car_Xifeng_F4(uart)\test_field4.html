<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试ThingSpeak Field4</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; max-height: 400px; overflow-y: auto; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🧪 ThingSpeak Field4 测试</h1>
    
    <div>
        <button onclick="testSendToField4()">📤 发送测试数据到Field4</button>
        <button onclick="testReadField4()">📥 读取Field4数据</button>
        <button onclick="clearField4()">🗑️ 清空Field4</button>
        <button onclick="clearLog()">🧹 清空日志</button>
    </div>
    
    <div class="log" id="log">
        <div>📋 测试日志：</div>
    </div>

    <script>
        // ThingSpeak配置
        const CHANNEL_ID = '3014831';
        const WRITE_API_KEY = 'LU22ZUP4ZTFK4IY9';
        const READ_API_KEY = 'V64RR7CZJ9Z4O7ED';
        
        // 发送测试数据到Field4
        async function testSendToField4() {
            log('🚀 开始发送测试数据到ThingSpeak Field4...');

            // 测试不同长度的数据格式
            const testFormats = [
                "WANDA_112.676903_26.881201_112.675797_26.886900",  // 原格式 (54字符)
                "WANDA_112.676903,26.881201,112.675797,26.886900",  // 逗号分隔 (52字符)
                "WANDA|112.676903|26.881201|112.675797|26.886900",  // 管道分隔 (52字符)
                "W_112.676903_26.881201_112.675797_26.886900",      // 短格式 (48字符)
                "112.676903,26.881201,112.675797,26.886900",        // 纯坐标 (46字符)
                "WANDA_TEST_123",                                    // 简单测试 (15字符)
                "TEST123"                                            // 最简测试 (7字符)
            ];

            log('🧪 将测试 ' + testFormats.length + ' 种数据格式...');

            for (let i = 0; i < testFormats.length; i++) {
                const testData = testFormats[i];
                log(`📤 测试格式 ${i+1}: ${testData} (${testData.length}字符)`);

                try {
                    // 使用图片标签发送（最可靠的方法）
                    const url = `https://api.thingspeak.com/update?api_key=${WRITE_API_KEY}&field4=${encodeURIComponent(testData)}`;

                    const img = new Image();
                    img.onload = function() {
                        log(`✅ 格式${i+1}发送成功`, 'success');
                    };
                    img.onerror = function() {
                        log(`ℹ️ 格式${i+1}请求完成`);
                    };
                    img.src = url;

                    // 每次发送间隔2秒，避免ThingSpeak限制
                    if (i < testFormats.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }

                } catch (error) {
                    log(`❌ 格式${i+1}发送失败: ${error.message}`, 'error');
                }
            }

            log('✅ 所有格式测试完成！', 'success');
            log('⏳ 请等待30秒后点击"读取Field4数据"查看哪种格式成功了');
        }

        // JSONP回调函数
        function handleResponse(data) {
            log('📡 JSONP响应: ' + JSON.stringify(data), 'success');
        }
        
        // 读取Field4数据
        async function testReadField4() {
            log('📥 开始读取ThingSpeak Field4数据...');
            
            try {
                const url = `https://api.thingspeak.com/channels/${CHANNEL_ID}/feeds/last.json?api_key=${READ_API_KEY}`;
                log('🔗 读取URL: ' + url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                log('📊 完整响应数据: ' + JSON.stringify(data, null, 2));
                
                if (data.field4) {
                    log('✅ Field4有数据: ' + data.field4, 'success');
                    log('📍 GPS数据: 纬度=' + data.field1 + ', 经度=' + data.field2);
                    log('⏰ 更新时间: ' + data.created_at);
                } else {
                    log('❌ Field4无数据', 'error');
                    log('🔍 可能原因: 数据还未上传或上传失败');
                }
                
            } catch (error) {
                log('❌ 读取失败: ' + error.message, 'error');
            }
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            logDiv.innerHTML += `<div class="${className}">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // 清空Field4
        function clearField4() {
            log('🗑️ 正在清空Field4...');

            const url = `https://api.thingspeak.com/update?api_key=${WRITE_API_KEY}&field4=`;

            const img = new Image();
            img.onload = function() {
                log('✅ Field4已清空', 'success');
            };
            img.onerror = function() {
                log('ℹ️ Field4清空请求已发送');
            };
            img.src = url;
        }

        // 清空日志
        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📋 测试日志：</div>';
        }
        
        // 页面加载完成
        window.onload = function() {
            log('🌐 Field4测试页面加载完成');
            log('📋 使用频道: ' + CHANNEL_ID);
            log('🔑 写入密钥: ' + WRITE_API_KEY);
        };
    </script>
</body>
</html>
