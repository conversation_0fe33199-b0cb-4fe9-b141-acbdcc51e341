<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试ThingSpeak Field4</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; max-height: 400px; overflow-y: auto; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🧪 ThingSpeak Field4 测试</h1>
    
    <div>
        <button onclick="testSendToField4()">📤 发送测试数据到Field4</button>
        <button onclick="testReadField4()">📥 读取Field4数据</button>
        <button onclick="clearLog()">🧹 清空日志</button>
    </div>
    
    <div class="log" id="log">
        <div>📋 测试日志：</div>
    </div>

    <script>
        // ThingSpeak配置
        const CHANNEL_ID = '3014831';
        const WRITE_API_KEY = 'LU22ZUP4ZTFK4IY9';
        const READ_API_KEY = 'V64RR7CZJ9Z4O7ED';
        
        // 发送测试数据到Field4
        async function testSendToField4() {
            log('🚀 开始发送测试数据到ThingSpeak Field4...');
            
            const testData = "WANDA_112.676903_26.881201_112.675797_26.886900";
            log('📤 测试数据: ' + testData);
            
            try {
                // 使用CORS代理发送数据
                const url = `https://api.thingspeak.com/update?api_key=${WRITE_API_KEY}&field4=${encodeURIComponent(testData)}`;
                log('🔗 请求URL: ' + url);
                
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'no-cors'  // 避免CORS问题
                });
                
                log('✅ 数据发送请求已发出', 'success');
                log('ℹ️ 由于CORS限制，无法获取响应状态');
                log('⏳ 请等待15秒后点击"读取Field4数据"查看结果');
                
            } catch (error) {
                log('❌ 发送失败: ' + error.message, 'error');
            }
        }
        
        // 读取Field4数据
        async function testReadField4() {
            log('📥 开始读取ThingSpeak Field4数据...');
            
            try {
                const url = `https://api.thingspeak.com/channels/${CHANNEL_ID}/feeds/last.json?api_key=${READ_API_KEY}`;
                log('🔗 读取URL: ' + url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                log('📊 完整响应数据: ' + JSON.stringify(data, null, 2));
                
                if (data.field4) {
                    log('✅ Field4有数据: ' + data.field4, 'success');
                    log('📍 GPS数据: 纬度=' + data.field1 + ', 经度=' + data.field2);
                    log('⏰ 更新时间: ' + data.created_at);
                } else {
                    log('❌ Field4无数据', 'error');
                    log('🔍 可能原因: 数据还未上传或上传失败');
                }
                
            } catch (error) {
                log('❌ 读取失败: ' + error.message, 'error');
            }
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            logDiv.innerHTML += `<div class="${className}">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📋 测试日志：</div>';
        }
        
        // 页面加载完成
        window.onload = function() {
            log('🌐 Field4测试页面加载完成');
            log('📋 使用频道: ' + CHANNEL_ID);
            log('🔑 写入密钥: ' + WRITE_API_KEY);
        };
    </script>
</body>
</html>
