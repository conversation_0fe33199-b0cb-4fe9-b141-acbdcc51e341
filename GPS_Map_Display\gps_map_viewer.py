#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS地图查看器 - 实时显示STM32 GPS定位数据
作者: Augment Agent
功能: 接收串口GPS数据，在OpenStreetMap上实时显示位置和轨迹
"""

# 尝试导入serial，如果失败则设置为None
try:
    import serial
    SERIAL_AVAILABLE = True
except ImportError:
    serial = None
    SERIAL_AVAILABLE = False
    print("⚠️ pyserial未安装，真实GPS功能不可用。使用模拟模式进行测试。")

import folium
import webbrowser
import os
import time
import threading
from datetime import datetime
import json

class GPSMapViewer:
    def __init__(self, port='COM6', baudrate=115200, simulation_mode=False):
        """
        初始化GPS地图查看器

        Args:
            port (str): 串口号，如 'COM6' (Windows) 或 '/dev/ttyUSB0' (Linux)
            baudrate (int): 波特率，默认115200
            simulation_mode (bool): 是否启用模拟模式（室内测试用）
        """
        self.port = port
        self.baudrate = baudrate
        self.serial_conn = None
        self.is_running = False
        self.simulation_mode = simulation_mode

        # GPS数据存储
        self.current_lat = 39.9042  # 默认位置：北京天安门
        self.current_lon = 116.4074
        self.gps_track = []  # 轨迹点列表
        self.last_update = None

        # 模拟数据配置
        self.sim_step = 0
        self.sim_route = self.generate_simulation_route()

        # 地图配置
        self.map_file = "gps_map.html"
        self.update_interval = 2  # 地图更新间隔(秒)

    def generate_simulation_route(self):
        """生成模拟GPS路线（北京市区路线）"""
        # 模拟从天安门到故宫再到王府井的路线
        route = [
            (39.9042, 116.4074),  # 天安门广场
            (39.9055, 116.4080),  # 向北移动
            (39.9070, 116.4085),  # 继续向北
            (39.9085, 116.4090),  # 接近故宫
            (39.9100, 116.4095),  # 故宫南门
            (39.9115, 116.4100),  # 故宫内部
            (39.9130, 116.4105),  # 故宫北部
            (39.9140, 116.4120),  # 向东移动
            (39.9145, 116.4140),  # 继续向东
            (39.9150, 116.4160),  # 接近王府井
            (39.9155, 116.4180),  # 王府井大街
            (39.9160, 116.4200),  # 王府井商业区
            (39.9165, 116.4220),  # 继续向东
            (39.9170, 116.4240),  # 东单方向
            (39.9175, 116.4260),  # 建国门方向
        ]
        return route

    def get_simulation_position(self):
        """获取模拟位置"""
        if self.sim_step >= len(self.sim_route):
            self.sim_step = 0  # 循环路线

        lat, lon = self.sim_route[self.sim_step]
        self.sim_step += 1

        # 添加一些随机偏移，模拟GPS精度误差
        import random
        lat += random.uniform(-0.0001, 0.0001)  # 约±10米的误差
        lon += random.uniform(-0.0001, 0.0001)

        return lat, lon

    def connect_serial(self):
        """连接串口"""
        if not SERIAL_AVAILABLE:
            print("❌ pyserial未安装，无法连接串口")
            return False

        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1
            )
            print(f"✅ 串口连接成功: {self.port} @ {self.baudrate}")
            return True
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            return False
    
    def parse_gps_data(self, line):
        """
        解析GPS数据
        
        期望格式: "GPS_MAP:48.116730,11.516666"
        
        Args:
            line (str): 串口接收的数据行
            
        Returns:
            tuple: (latitude, longitude) 或 None
        """
        try:
            line = line.strip()
            if line.startswith("GPS_MAP:"):
                # 提取坐标数据
                coords = line.replace("GPS_MAP:", "")
                lat, lon = coords.split(",")
                
                latitude = float(lat)
                longitude = float(lon)
                
                # 简单的坐标有效性检查
                if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                    return latitude, longitude
                else:
                    print(f"⚠️ 无效坐标: {latitude}, {longitude}")
                    return None
            return None
        except Exception as e:
            print(f"⚠️ GPS数据解析错误: {e}")
            return None
    
    def update_position(self, lat, lon):
        """更新当前位置"""
        self.current_lat = lat
        self.current_lon = lon
        self.last_update = datetime.now()
        
        # 添加到轨迹
        self.gps_track.append({
            'lat': lat,
            'lon': lon,
            'time': self.last_update.strftime("%H:%M:%S")
        })
        
        # 限制轨迹点数量（保留最近100个点）
        if len(self.gps_track) > 100:
            self.gps_track.pop(0)
        
        print(f"📍 位置更新: {lat:.6f}, {lon:.6f} @ {self.last_update.strftime('%H:%M:%S')}")
    
    def create_map(self):
        """创建地图"""
        # 创建地图，以当前位置为中心
        m = folium.Map(
            location=[self.current_lat, self.current_lon],
            zoom_start=15,
            tiles='OpenStreetMap'
        )
        
        # 添加当前位置标记
        folium.Marker(
            [self.current_lat, self.current_lon],
            popup=f"当前位置<br>纬度: {self.current_lat:.6f}<br>经度: {self.current_lon:.6f}<br>时间: {self.last_update.strftime('%H:%M:%S') if self.last_update else 'N/A'}",
            tooltip="GPS当前位置",
            icon=folium.Icon(color='red', icon='info-sign')
        ).add_to(m)
        
        # 添加轨迹线
        if len(self.gps_track) > 1:
            track_coords = [[point['lat'], point['lon']] for point in self.gps_track]
            folium.PolyLine(
                track_coords,
                color='blue',
                weight=3,
                opacity=0.8,
                popup="GPS轨迹"
            ).add_to(m)
        
        # 添加轨迹点
        for i, point in enumerate(self.gps_track):
            if i == len(self.gps_track) - 1:  # 最新点
                color = 'red'
                icon = 'star'
            else:  # 历史点
                color = 'blue'
                icon = 'circle'
            
            folium.CircleMarker(
                [point['lat'], point['lon']],
                radius=3,
                popup=f"时间: {point['time']}",
                color=color,
                fill=True,
                fillColor=color
            ).add_to(m)
        
        # 添加信息面板
        info_html = f"""
        <div style="position: fixed; 
                    top: 10px; left: 50px; width: 300px; height: 120px; 
                    background-color: white; border:2px solid grey; z-index:9999; 
                    font-size:14px; padding: 10px">
        <h4>GPS信息</h4>
        <p><b>纬度:</b> {self.current_lat:.6f}</p>
        <p><b>经度:</b> {self.current_lon:.6f}</p>
        <p><b>轨迹点:</b> {len(self.gps_track)}</p>
        <p><b>更新时间:</b> {self.last_update.strftime('%H:%M:%S') if self.last_update else 'N/A'}</p>
        </div>
        """
        m.get_root().html.add_child(folium.Element(info_html))
        
        return m
    
    def save_and_open_map(self):
        """保存并打开地图"""
        try:
            map_obj = self.create_map()
            map_obj.save(self.map_file)
            
            # 在浏览器中打开地图
            file_path = os.path.abspath(self.map_file)
            webbrowser.open(f'file://{file_path}')
            print(f"🗺️ 地图已保存并打开: {self.map_file}")
        except Exception as e:
            print(f"❌ 地图创建失败: {e}")
    
    def read_serial_data(self):
        """读取串口数据的线程函数"""
        while self.is_running:
            try:
                if self.simulation_mode:
                    # 模拟模式：生成模拟GPS数据
                    lat, lon = self.get_simulation_position()
                    self.update_position(lat, lon)
                    time.sleep(3)  # 模拟模式每3秒更新一次位置

                elif self.serial_conn and self.serial_conn.in_waiting > 0:
                    # 真实模式：从串口读取数据
                    line = self.serial_conn.readline().decode('utf-8', errors='ignore')

                    # 解析GPS数据
                    coords = self.parse_gps_data(line)
                    if coords:
                        lat, lon = coords
                        self.update_position(lat, lon)

                if not self.simulation_mode:
                    time.sleep(0.1)  # 避免CPU占用过高

            except Exception as e:
                print(f"⚠️ 数据读取错误: {e}")
                time.sleep(1)
    
    def update_map_periodically(self):
        """定期更新地图的线程函数"""
        while self.is_running:
            try:
                if self.last_update:  # 只有在有GPS数据时才更新地图
                    map_obj = self.create_map()
                    map_obj.save(self.map_file)
                    print(f"🔄 地图已更新 @ {datetime.now().strftime('%H:%M:%S')}")
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                print(f"⚠️ 地图更新错误: {e}")
                time.sleep(self.update_interval)
    
    def start(self):
        """启动GPS地图查看器"""
        print("🚀 启动GPS地图查看器...")

        if self.simulation_mode:
            print("🎭 模拟模式已启用（室内测试）")
            print("📍 模拟路线：北京市区（天安门→故宫→王府井）")
        else:
            # 连接串口
            if not self.connect_serial():
                print("❌ 无法连接串口，切换到模拟模式")
                self.simulation_mode = True

        # 创建初始地图
        self.save_and_open_map()

        # 启动数据读取和地图更新线程
        self.is_running = True

        serial_thread = threading.Thread(target=self.read_serial_data, daemon=True)
        map_thread = threading.Thread(target=self.update_map_periodically, daemon=True)

        serial_thread.start()
        map_thread.start()

        if self.simulation_mode:
            print("🎭 开始模拟GPS数据...")
            print("📍 每3秒更新一次位置")
        else:
            print("📡 开始接收GPS数据...")

        print("🗺️ 地图将每2秒自动更新")
        print("💡 刷新浏览器页面查看最新位置")
        print("⏹️ 按 Ctrl+C 退出程序")

        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 程序停止")
            self.stop()
    
    def stop(self):
        """停止GPS地图查看器"""
        self.is_running = False
        if self.serial_conn:
            self.serial_conn.close()
        print("✅ GPS地图查看器已停止")

def main():
    """主函数"""
    print("=" * 50)
    print("🗺️ GPS地图查看器")
    print("=" * 50)

    # 配置串口（根据你的实际情况修改）
    PORT = 'COM6'  # Windows: COM6, Linux: /dev/ttyUSB0
    BAUDRATE = 115200

    # 询问用户是否使用模拟模式
    print("🤔 选择运行模式:")
    print("1. 真实GPS模式 (需要STM32连接)")
    print("2. 模拟GPS模式 (室内测试)")

    try:
        choice = input("请输入选择 (1/2，默认2): ").strip()
        if choice == "1":
            simulation_mode = False
            print(f"📡 串口配置: {PORT} @ {BAUDRATE}")
            print("💡 请确保STM32已连接并发送GPS数据")
            print("📍 数据格式: GPS_MAP:纬度,经度")
        else:
            simulation_mode = True
            print("🎭 已选择模拟模式")
            print("📍 将模拟北京市区GPS轨迹")
    except:
        simulation_mode = True
        print("🎭 默认使用模拟模式")

    print("-" * 50)

    # 创建并启动GPS地图查看器
    viewer = GPSMapViewer(port=PORT, baudrate=BAUDRATE, simulation_mode=simulation_mode)
    viewer.start()

if __name__ == "__main__":
    main()
