<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>GPS追踪系统 - 高德地图</title>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=946b162f62c7b5af1892cc6fc00b2ea1&plugin=AMap.Scale,AMap.OverView,AMap.ToolBar,AMap.MapType,AMap.PolyEditor,AMap.CircleEditor,AMap.Driving,AMap.Walking"></script>
    <script type="text/javascript">
        // 设置安全密钥
        window._AMapSecurityConfig = {
            securityJsCode: '7f0e1e6c07a1c98376835485793c6872'
        };
    </script>
    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .header {
            background-color: #1890ff;
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .info-panel {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item {
            text-align: center;
            flex: 1;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .status-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        #container {
            width: calc(100% - 20px);
            height: 500px;
            margin: 10px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .controls {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #40a9ff;
        }
        
        .online {
            color: #52c41a;
        }
        
        .offline {
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚗 GPS追踪系统 - 高德地图版</h1>
        <div>衡阳市体育中心 GPS 实时定位</div>
    </div>

    <div class="info-panel">
        <div class="status">
            <div class="status-item">
                <div class="status-value" id="latitude">26.885054837223990</div>
                <div class="status-label">纬度 (°N)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="longitude">112.679572502899990</div>
                <div class="status-label">经度 (°E)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="altitude">68.0</div>
                <div class="status-label">海拔 (m)</div>
            </div>
            <div class="status-item">
                <div class="status-value online" id="status">在线</div>
                <div class="status-label">状态</div>
            </div>
        </div>
        <div style="text-align: center; margin-top: 10px;">
            <span>📍 当前位置：衡阳市体育中心</span>
            <span style="margin-left: 20px;">⏰ 更新时间：<span id="updateTime">--</span></span>
        </div>
    </div>
    
    <div id="container"></div>
    
    <div class="controls">
        <button class="btn" onclick="centerMap()">📍 回到中心</button>
        <button class="btn" onclick="toggleTracking()">📊 轨迹追踪</button>
        <button class="btn" onclick="simulateSerialInput()">🎯 测试WANDA导航</button>
        <button class="btn" onclick="openThingSpeak()">�️ 高德API</button>
    </div>

    <script type="text/javascript">
        var map, marker, trackingPath = [];
        var isTracking = false;

        // 导航相关变量
        var driving = null;
        var navigationRoute = null;
        var startMarker = null;
        var endMarker = null;
        var isNavigating = false;
        
        // 初始化地图
        function initMap() {
            map = new AMap.Map('container', {
                resizeEnable: true,
                center: [112.679572502899990, 26.885054837223990], // 衡阳市体育中心坐标
                zoom: 16,
                mapStyle: 'amap://styles/normal'
            });

            // 添加地图控件
            map.addControl(new AMap.Scale());
            map.addControl(new AMap.OverView({isOpen:true}));
            map.addControl(new AMap.ToolBar());
            map.addControl(new AMap.MapType({
                defaultType: 0,
                showTraffic: false,
                showRoad: true
            }));

            // 初始化路径规划服务
            driving = new AMap.Driving({
                map: map,
                panel: null, // 不显示详细路径面板
                hideMarkers: false, // 显示起终点标记
                showTraffic: true, // 显示实时路况
                policy: AMap.DrivingPolicy.LEAST_TIME // 最短时间策略
            });

            // 创建标记点
            marker = new AMap.Marker({
                position: [112.679572502899990, 26.885054837223990],
                title: '衡阳市体育中心 GPS追踪',
                icon: new AMap.Icon({
                    size: new AMap.Size(32, 32),
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
                })
            });

            map.add(marker);

            // 添加信息窗体
            var infoWindow = new AMap.InfoWindow({
                content: '<div style="padding:10px;"><h4>�️ 衡阳市体育中心</h4><p>📍 GPS实时定位点</p><p>🕐 最后更新：' + new Date().toLocaleString() + '</p></div>'
            });

            marker.on('click', function() {
                infoWindow.open(map, marker.getPosition());
            });
        }
        
        // 刷新数据（仅用于手动刷新显示）
        function refreshData() {
            // 不再模拟GPS数据，只刷新当前显示
            centerMap();
        }





        // 解析GPS数据 - 专用于高德地图GPS追踪系统
        function parseGPSData(data) {
            try {
                console.log('高德地图接收GPS数据:', data);

                // 格式1: "AMAP_GPS:26.885054,112.679572,68.0" (高德地图专用格式)
                if (data.includes('AMAP_GPS:')) {
                    const coords = data.replace('AMAP_GPS:', '').trim();
                    const parts = coords.split(',');
                    if (parts.length >= 2) {
                        const lat = parseFloat(parts[0]);
                        const lon = parseFloat(parts[1]);
                        const alt = parts.length > 2 ? parseFloat(parts[2]) : 68.0;

                        if (!isNaN(lat) && !isNaN(lon)) {
                            updateRealTimeGPS(lat, lon, alt);
                            console.log('高德地图位置更新成功:', lat, lon, alt);
                            return;
                        }
                    }
                }

                // 格式2: "GPS_MAP:26.885054,112.679572" (兼容格式)
                if (data.includes('GPS_MAP:')) {
                    const coords = data.replace('GPS_MAP:', '').trim();
                    const parts = coords.split(',');
                    if (parts.length >= 2) {
                        const lat = parseFloat(parts[0]);
                        const lon = parseFloat(parts[1]);
                        const alt = parts.length > 2 ? parseFloat(parts[2]) : 68.0;

                        if (!isNaN(lat) && !isNaN(lon)) {
                            updateRealTimeGPS(lat, lon, alt);
                            return;
                        }
                    }
                }

                // 格式2: "LAT:26.885054,LON:112.679572,ALT:68.5"
                if (data.includes('LAT:') && data.includes('LON:')) {
                    const latMatch = data.match(/LAT:([\d.-]+)/);
                    const lonMatch = data.match(/LON:([\d.-]+)/);
                    const altMatch = data.match(/ALT:([\d.-]+)/);

                    if (latMatch && lonMatch) {
                        const lat = parseFloat(latMatch[1]);
                        const lon = parseFloat(lonMatch[1]);
                        const alt = altMatch ? parseFloat(altMatch[1]) : 68.0;

                        updateRealTimeGPS(lat, lon, alt);
                        return;
                    }
                }

                // 格式3: NMEA格式解析
                if (data.startsWith('$GPGGA') || data.startsWith('$GPRMC') || data.startsWith('$GNRMC')) {
                    parseNMEAData(data);
                    return;
                }

                // 格式4: 简单的经纬度格式 "26.885054,112.679572"
                const coords = data.trim().split(',');
                if (coords.length >= 2) {
                    const lat = parseFloat(coords[0]);
                    const lon = parseFloat(coords[1]);
                    const alt = coords.length > 2 ? parseFloat(coords[2]) : 68.0;

                    if (!isNaN(lat) && !isNaN(lon) && lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180) {
                        updateRealTimeGPS(lat, lon, alt);
                        return;
                    }
                }

                console.log('未识别的GPS数据格式:', data);

            } catch (error) {
                console.error('GPS数据解析失败:', error);
            }
        }

        // 解析NMEA格式数据
        function parseNMEAData(nmea) {
            try {
                const parts = nmea.split(',');

                if (nmea.startsWith('$GPGGA') && parts.length >= 6) {
                    // $GPGGA格式解析
                    const lat = parseNMEACoordinate(parts[2], parts[3]);
                    const lon = parseNMEACoordinate(parts[4], parts[5]);
                    const alt = parseFloat(parts[9]) || 68.0;

                    if (lat && lon) {
                        updateRealTimeGPS(lat, lon, alt);
                    }
                }
            } catch (error) {
                console.error('NMEA数据解析失败:', error);
            }
        }

        // 解析NMEA坐标格式
        function parseNMEACoordinate(coord, direction) {
            if (!coord || !direction) return null;

            const degrees = Math.floor(parseFloat(coord) / 100);
            const minutes = parseFloat(coord) % 100;
            let decimal = degrees + minutes / 60;

            if (direction === 'S' || direction === 'W') {
                decimal = -decimal;
            }

            return decimal;
        }

        // 更新实时GPS数据
        function updateRealTimeGPS(lat, lon, alt) {
            // 更新显示
            updateGPSDisplay(lat, lon, alt);
            updateMapPosition(lon, lat);

            // 上传数据 - 已禁用，只从ESP01读取数据，不上传
            // uploadGPSData(lat, lon, alt);

            // 更新时间戳 - 只在接收到真实GPS数据时更新
            var now = new Date();
            var formattedDate = now.getFullYear() + "/" +
                               padZero(now.getMonth() + 1) + "/" +
                               padZero(now.getDate()) + " " +
                               padZero(now.getHours()) + ":" +
                               padZero(now.getMinutes()) + ":" +
                               padZero(now.getSeconds());

            document.getElementById('updateTime').textContent = formattedDate;
            document.getElementById('status').textContent = '实时GPS';
            document.getElementById('status').className = 'status-value online';

            // 保存最后更新时间到localStorage
            localStorage.setItem('lastGpsUpdateTime', formattedDate);
        }

        // 辅助函数：补零
        function padZero(num) {
            return num < 10 ? '0' + num : num;
        }

        // 从ThingSpeak获取ESP01上传的GPS数据 (使用fetch方式)
        function loadGPSFromThingSpeak() {
            console.log('🔄 正在从ThingSpeak获取GPS数据...');

            var thingSpeakURL = 'https://api.thingspeak.com/channels/3014831/feeds/last.json?api_key=LU22ZUP4ZTFK4IY9';

            fetch(thingSpeakURL)
                .then(response => {
                    console.log('ThingSpeak响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('✅ ThingSpeak原始数据:', data);

                    // 检查是否返回-1（表示没有数据或频道私有）
                    if (data === -1) {
                        console.log('❌ ThingSpeak返回-1，可能是频道私有或没有数据');
                        document.getElementById('status').textContent = '频道无数据';
                        document.getElementById('status').className = 'status-value offline';

                        // 使用默认的衡阳师范学院位置
                        console.log('🏫 使用默认位置：衡阳师范学院');
                        updateRealTimeGPS(26.8812, 112.6769, 68.0);
                        document.getElementById('status').textContent = '使用默认位置';
                        return;
                    }

                    if (data && data.field1 && data.field2) {
                        var lat = parseFloat(data.field1);
                        var lon = parseFloat(data.field2);

                        // 解析Field3：格式为 "高度|导航指令" 或 "高度"
                        var alt = 68.0;
                        var navigationCmd = null;

                        if (data.field3) {
                            var field3Parts = data.field3.toString().split('|');
                            alt = parseFloat(field3Parts[0]) || 68.0;
                            if (field3Parts.length > 1 && field3Parts[1] !== '') {
                                navigationCmd = field3Parts[1];
                            }
                        }

                        console.log('📍 从ThingSpeak读取GPS数据:', lat, lon, alt);
                        console.log('⏰ 数据时间:', data.created_at);
                        if (navigationCmd) {
                            console.log('🗺️ 检测到导航指令:', navigationCmd);
                        }

                        // 更新地图位置
                        updateRealTimeGPS(lat, lon, alt);

                        // 处理导航指令
                        if (navigationCmd) {
                            processNavigationData(navigationCmd);
                        }

                        // 更新状态为在线
                        document.getElementById('status').textContent = 'ESP01在线';
                        document.getElementById('status').className = 'status-value online';

                        // 更新时间显示
                        var updateTime = new Date(data.created_at).toLocaleString('zh-CN');
                        document.getElementById('updateTime').textContent = updateTime;
                        localStorage.setItem('lastGpsUpdateTime', updateTime);

                        console.log('✅ GPS数据更新成功！');
                    } else {
                        console.log('❌ ThingSpeak数据格式不正确:', data);
                        document.getElementById('status').textContent = '数据格式错误';
                        document.getElementById('status').className = 'status-value offline';

                        // 使用默认位置
                        updateRealTimeGPS(26.8812, 112.6769, 68.0);
                    }
                })
                .catch(error => {
                    console.log('❌ ThingSpeak请求失败:', error.message);
                    console.log('🔄 使用本地模拟数据');
                    document.getElementById('status').textContent = '本地模拟';
                    document.getElementById('status').className = 'status-value online';

                    // 使用本地模拟GPS数据（模拟ESP01的行为）
                    simulateLocalGPS();
                });
        }

        // 本地GPS模拟函数（模拟ESP01的行为）
        function simulateLocalGPS() {
            // 模拟ESP01生成的GPS数据（衡阳师范学院附近）
            var baseLat = 26.8812;
            var baseLon = 112.6769;
            var baseAlt = 68.0;

            // 添加小幅度随机变化（模拟真实GPS的微小漂移）
            var seed = Date.now() % 1000;
            var lat = baseLat + (seed % 100) * 0.0001;
            var lon = baseLon + (seed % 100) * 0.0001;
            var alt = baseAlt + (seed % 50);

            console.log('本地模拟GPS数据:', lat, lon, alt);

            // 更新地图位置
            updateRealTimeGPS(lat, lon, alt);

            // 更新状态
            document.getElementById('status').textContent = '本地模拟';
            document.getElementById('status').className = 'status-value online';

            // 更新时间显示
            var updateTime = new Date().toLocaleString('zh-CN');
            document.getElementById('updateTime').textContent = updateTime;
            localStorage.setItem('lastGpsUpdateTime', updateTime);
        }

        // 实时上传GPS数据到ThingSpeak和高德地图
        function uploadGPSData(lat, lon, alt) {
            // 上传到ThingSpeak (使用您的API Key)
            var thingSpeakURL = 'https://api.thingspeak.com/update?api_key=LU22ZUP4ZTFK4IY9';
            thingSpeakURL += '&field1=' + lat.toFixed(6);
            thingSpeakURL += '&field2=' + lon.toFixed(6);

            // Field3格式：高度|导航指令（如果有的话）
            var field3Value = alt.toFixed(1);
            if (window.pendingNavigationCommand) {
                field3Value += '|' + window.pendingNavigationCommand;
                window.pendingNavigationCommand = null; // 清除待发送的指令
            }
            thingSpeakURL += '&field3=' + encodeURIComponent(field3Value);

            // 使用JSONP方式上传数据
            var script = document.createElement('script');
            script.src = thingSpeakURL + '&callback=uploadCallback';
            document.head.appendChild(script);
            document.head.removeChild(script);

            // 同时在控制台显示上传信息
            console.log('高德地图GPS数据已上传:', {
                latitude: lat.toFixed(6),
                longitude: lon.toFixed(6),
                altitude: alt.toFixed(1),
                timestamp: new Date().toLocaleString(),
                location: '衡阳市体育中心',
                mapAPI: '高德地图API Key: 946b162f62c7b5af1892cc6fc00b2ea1',
                thingSpeakAPI: 'LU22ZUP4ZTFK4IY9'
            });
        }

        // 上传回调函数
        function uploadCallback(response) {
            if (response > 0) {
                console.log('ThingSpeak上传成功，Entry ID:', response);
            } else {
                console.log('ThingSpeak上传失败');
            }
        }

        // 更新GPS显示
        function updateGPSDisplay(lat, lon, alt) {
            document.getElementById('latitude').textContent = lat.toFixed(6);
            document.getElementById('longitude').textContent = lon.toFixed(6);
            document.getElementById('altitude').textContent = alt.toFixed(1);
        }
        
        // 更新地图位置
        function updateMapPosition(lon, lat) {
            var newPos = [lon, lat];
            marker.setPosition(newPos);
            
            if (isTracking) {
                trackingPath.push(newPos);
                if (trackingPath.length > 1) {
                    var polyline = new AMap.Polyline({
                        path: trackingPath,
                        strokeColor: '#1890ff',
                        strokeWeight: 3,
                        strokeOpacity: 0.8
                    });
                    map.add(polyline);
                }
            }
        }
        
        // 刷新数据
        function refreshData() {
            fetchGPSData();
        }
        
        // 回到地图中心
        function centerMap() {
            map.setCenter(marker.getPosition());
            map.setZoom(16);
        }
        
        // 切换轨迹追踪
        function toggleTracking() {
            isTracking = !isTracking;
            if (isTracking) {
                trackingPath = [marker.getPosition()];
                alert('轨迹追踪已开启');
            } else {
                alert('轨迹追踪已关闭');
            }
        }
        
        // 打开ThingSpeak
        function openThingSpeak() {
            window.open('https://thingspeak.com/channels/3014831', '_blank');
        }
        


        // 初始化
        window.onload = function() {
            initMap();

            // 从localStorage恢复最后更新时间
            var lastUpdateTime = localStorage.getItem('lastGpsUpdateTime');
            if (lastUpdateTime) {
                document.getElementById('updateTime').textContent = lastUpdateTime;
            } else {
                document.getElementById('updateTime').textContent = '--';
            }

            // 立即加载一次ThingSpeak数据
            loadGPSFromThingSpeak();

            // 每10秒从ThingSpeak获取一次GPS数据
            setInterval(loadGPSFromThingSpeak, 10000);

            console.log('高德地图GPS追踪系统已初始化');
            console.log('正在从ThingSpeak获取ESP01上传的GPS数据...');

            // 添加测试按钮
            addTestButton();
        };

        // 添加测试按钮
        function addTestButton() {
            var testButton = document.createElement('button');
            testButton.innerHTML = '🧪 测试万达导航API';
            testButton.style.position = 'fixed';
            testButton.style.top = '10px';
            testButton.style.right = '10px';
            testButton.style.zIndex = '99999';  // 更高的z-index
            testButton.style.padding = '10px 20px';
            testButton.style.backgroundColor = '#ff4d4f';
            testButton.style.color = 'white';
            testButton.style.border = 'none';
            testButton.style.borderRadius = '4px';
            testButton.style.cursor = 'pointer';
            testButton.style.fontSize = '14px';
            testButton.style.boxShadow = '0 2px 8px rgba(0,0,0,0.3)';

            // 防止事件冒泡和默认行为
            testButton.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('🧪 开始测试万达导航API...');
                alert('🚀 开始测试高德地图API调用！请查看控制台日志');

                // 模拟从ThingSpeak获取的导航数据
                var testNavigationData = "WANDA_112.676903_26.881201_112.675797_26.886900";
                console.log('📥 模拟导航数据:', testNavigationData);
                console.log('🔍 这将直接调用高德地图API进行路径规划');

                // 直接调用处理函数
                processNavigationData(testNavigationData);

                return false;
            };

            document.body.appendChild(testButton);
        }

        // 处理导航数据
        function processNavigationData(navigationStr) {
            try {
                console.log('🔍 解析导航数据:', navigationStr);
                console.log('🔍 数据类型:', typeof navigationStr);
                console.log('🔍 数据长度:', navigationStr.length);

                // 解析格式: WANDA_start_lon_start_lat_end_lon_end_lat (经度在前，纬度在后)
                if (navigationStr.startsWith('WANDA_')) {
                    console.log('✅ 检测到WANDA导航数据');
                    var coords = navigationStr.substring(6).split('_'); // 去掉 "WANDA_" 前缀
                    console.log('🔍 分割后的坐标数组:', coords);

                    if (coords.length >= 4) {
                        var startLon = parseFloat(coords[0]); // 起点经度
                        var startLat = parseFloat(coords[1]); // 起点纬度
                        var endLon = parseFloat(coords[2]);   // 终点经度
                        var endLat = parseFloat(coords[3]);   // 终点纬度

                        console.log('📍 万达导航路径 (经度,纬度):', startLon, startLat, '->', endLon, endLat);
                        console.log('🚀 准备调用高德地图API...');

                        // 使用高德地图API显示导航路线
                        showAmapNavigationRoute(startLon, startLat, endLon, endLat);
                    } else {
                        console.error('❌ 坐标数据不足，需要4个坐标值，实际获得:', coords.length);
                    }
                } else {
                    console.log('ℹ️ 不是WANDA导航数据，跳过处理');
                }
            } catch (error) {
                console.error('❌ 导航数据解析失败:', error);
                console.log('原始数据:', navigationStr);
            }
        }

        // 使用高德地图JavaScript API显示导航路线
        function showAmapNavigationRoute(startLon, startLat, endLon, endLat) {
            try {
                console.log('🗺️ 开始使用高德地图JavaScript API规划路线...');
                console.log('📍 起点坐标:', startLon, startLat);
                console.log('📍 终点坐标:', endLon, endLat);
                console.log('🔍 检查AMap对象:', typeof AMap);

                // 清除之前的导航路线
                clearNavigationRoute();

                // 使用高德地图的步行路径规划 (适用于Web端JS API)
                if (typeof AMap !== 'undefined') {
                    console.log('✅ AMap对象存在，开始创建Walking实例...');

                    var walking = new AMap.Walking({
                        map: map,
                        panel: "panel"
                    });

                    console.log('✅ Walking实例创建成功');
                    console.log('🚀 开始调用walking.search API...');

                    // 规划步行路径
                    walking.search(new AMap.LngLat(startLon, startLat), new AMap.LngLat(endLon, endLat), function(status, result) {
                        console.log('📡 高德地图API回调 - 状态:', status);

                        if (status === 'complete') {
                            console.log('✅ 高德地图JavaScript API路径规划成功');
                            console.log('� 路径信息:', result);

                            // 获取路径信息
                            var route = result.routes[0];
                            var distance = (route.distance / 1000).toFixed(1); // 转换为公里
                            var time = Math.round(route.time / 60); // 转换为分钟

                            // 显示导航信息
                            showNavigationInfo(distance, time);

                            // 调整地图视野以显示完整路径
                            map.setFitView();

                            isNavigating = true;
                            console.log('🎯 万达导航已启动！距离:' + distance + 'km, 时间:' + time + '分钟');

                        } else {
                            console.error('❌ 高德地图JavaScript API路径规划失败:', status);
                            // 备选方案：显示简单直线路径
                            displaySimpleRoute(startLat, startLon, endLat, endLon);
                        }
                    });
                } else {
                    console.error('❌ 高德地图JavaScript API未加载');
                    // 备选方案：显示简单直线路径
                    displaySimpleRoute(startLat, startLon, endLat, endLon);
                }

            } catch (error) {
                console.error('❌ 高德地图JavaScript API调用失败:', error);
                // 备选方案：显示简单直线路径
                displaySimpleRoute(startLat, startLon, endLat, endLon);
            }
        }

        // 显示简单直线路径（备选方案）
        function displaySimpleRoute(startLat, startLon, endLat, endLon) {
            console.log('📍 显示简单直线路径作为备选方案');

            if (map) {
                // 添加起点和终点标记
                var startMarker = new AMap.Marker({
                    position: new AMap.LngLat(startLon, startLat),
                    title: '起点: 当前位置'
                });
                map.add(startMarker);

                var endMarker = new AMap.Marker({
                    position: new AMap.LngLat(endLon, endLat),
                    title: '终点: 万达广场'
                });
                map.add(endMarker);

                // 添加直线路径
                var polyline = new AMap.Polyline({
                    path: [
                        new AMap.LngLat(startLon, startLat),
                        new AMap.LngLat(endLon, endLat)
                    ],
                    strokeColor: '#3366FF',
                    strokeWeight: 4,
                    strokeOpacity: 0.8
                });
                map.add(polyline);

                // 调整地图视野
                map.setFitView([startMarker, endMarker, polyline]);

                console.log('✅ 简单直线路径已显示');
            }
        }

        // 显示导航路线（备用方案）
        function showNavigationRoute(startLat, startLon, endLat, endLon) {
            try {
                console.log('🗺️ 开始显示导航路线...');

                // 清除之前的导航路线
                clearNavigationRoute();

                // 创建起点和终点
                var startPoint = new AMap.LngLat(startLon, startLat);
                var endPoint = new AMap.LngLat(endLon, endLat);

                console.log('🚗 起点:', startPoint.toString());
                console.log('🏢 终点:', endPoint.toString());

                // 使用高德地图路径规划
                driving.search(startPoint, endPoint, function(status, result) {
                    if (status === 'complete') {
                        console.log('✅ 路径规划成功');
                        console.log('📊 路径信息:', result);

                        // 获取路径信息
                        var route = result.routes[0];
                        var distance = (route.distance / 1000).toFixed(1); // 转换为公里
                        var time = Math.round(route.time / 60); // 转换为分钟

                        // 显示导航信息
                        showNavigationInfo(distance, time);

                        // 调整地图视野以显示完整路径
                        map.setFitView();

                        isNavigating = true;

                    } else {
                        console.error('❌ 路径规划失败:', status);
                        // 备选方案：显示直线路径
                        showStraightLineRoute(startLat, startLon, endLat, endLon);
                    }
                });

            } catch (error) {
                console.error('❌ 显示导航路线失败:', error);
            }
        }

        // 显示直线路径（备选方案）
        function showStraightLineRoute(startLat, startLon, endLat, endLon) {
            console.log('⚠️ 使用直线路径（备选方案）');

            // 创建直线路径
            var lineArr = [
                [startLon, startLat],
                [endLon, endLat]
            ];

            var polyline = new AMap.Polyline({
                path: lineArr,
                strokeColor: "#FF6B35",
                strokeWeight: 6,
                strokeOpacity: 0.8,
                strokeStyle: "dashed"
            });

            map.add(polyline);
            navigationRoute = polyline;

            // 添加起点和终点标记
            startMarker = new AMap.Marker({
                position: [startLon, startLat],
                title: '起点：当前位置',
                icon: new AMap.Icon({
                    size: new AMap.Size(32, 32),
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/start.png'
                })
            });

            endMarker = new AMap.Marker({
                position: [endLon, endLat],
                title: '终点：酃湖万达广场',
                icon: new AMap.Icon({
                    size: new AMap.Size(32, 32),
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/end.png'
                })
            });

            map.add([startMarker, endMarker]);

            // 调整地图视野
            map.setFitView([polyline, startMarker, endMarker]);

            // 计算直线距离
            var distance = AMap.GeometryUtil.distance([startLon, startLat], [endLon, endLat]);
            showNavigationInfo((distance / 1000).toFixed(1), '未知');

            isNavigating = true;
        }

        // 显示导航信息
        function showNavigationInfo(distance, time) {
            var infoHtml = `
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 10px; margin: 10px 0;">
                    <h4 style="margin: 0 0 10px 0; color: #856404;">🗺️ 导航信息</h4>
                    <p style="margin: 5px 0;"><strong>目的地:</strong> 酃湖万达广场</p>
                    <p style="margin: 5px 0;"><strong>距离:</strong> ${distance} 公里</p>
                    <p style="margin: 5px 0;"><strong>预计时间:</strong> ${time} 分钟</p>
                    <button onclick="clearNavigationRoute()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">结束导航</button>
                </div>
            `;

            // 在信息面板中显示导航信息
            var infoPanel = document.querySelector('.info-panel');
            var existingNavInfo = document.getElementById('nav-info');
            if (existingNavInfo) {
                existingNavInfo.remove();
            }

            var navInfoDiv = document.createElement('div');
            navInfoDiv.id = 'nav-info';
            navInfoDiv.innerHTML = infoHtml;
            infoPanel.appendChild(navInfoDiv);
        }

        // 处理串口输入命令
        function handleSerialCommand(command) {
            console.log('📡 收到串口命令:', command);

            if (command.toLowerCase() === 'wanda') {
                console.log('🎯 检测到WANDA导航命令');

                // 获取当前GPS位置
                var currentLat = window.currentGPS ? window.currentGPS.lat : 26.881201;
                var currentLon = window.currentGPS ? window.currentGPS.lon : 112.676903;
                var currentAlt = window.currentGPS ? window.currentGPS.alt : 68.0;

                // 万达广场坐标
                var wandaLat = 26.886900;
                var wandaLon = 112.675797;

                console.log('📍 当前位置:', currentLat, currentLon);
                console.log('🎯 目标位置:', wandaLat, wandaLon);

                // 立即使用高德地图驾车路径规划
                startAmapDrivingNavigation(currentLon, currentLat, wandaLon, wandaLat);

                // 构建导航数据字符串用于上传
                var navigationData = `WANDA_${currentLon}_${currentLat}_${wandaLon}_${wandaLat}`;
                console.log('🗺️ 生成导航数据:', navigationData);

                // 立即上传包含导航指令的GPS数据到ThingSpeak
                uploadNavigationData(currentLat, currentLon, currentAlt, navigationData);

                console.log('✅ WANDA导航指令已上传到ThingSpeak');

                // 显示提示信息
                alert('🎯 WANDA导航已启动！\n目的地：酃湖万达广场\n正在规划驾车路线...\n导航数据已上传到ThingSpeak');

            } else {
                console.log('ℹ️ 未知命令:', command);
            }
        }

        // 启动高德地图驾车导航
        function startAmapDrivingNavigation(startLon, startLat, endLon, endLat) {
            console.log('🚗 启动高德地图驾车导航...');
            console.log('📍 起点:', startLon, startLat);
            console.log('🎯 终点:', endLon, endLat);

            try {
                // 清除之前的导航路线
                clearNavigationRoute();

                console.log('🔍 检查AMap对象:', typeof AMap);
                console.log('🔍 检查map对象:', typeof map);

                if (typeof AMap !== 'undefined' && map) {
                    console.log('✅ AMap和map对象都存在，创建驾车路径规划...');

                    // 创建驾车路径规划实例
                    console.log('🔧 创建AMap.Driving实例...');
                    driving = new AMap.Driving({
                        map: map,
                        showTraffic: true,  // 显示实时路况
                        hideMarkers: false, // 显示起终点标记
                        autoFitView: true   // 自动调整视野
                    });

                    console.log('✅ AMap.Driving实例创建成功');
                    console.log('🚀 开始驾车路径规划...');

                    // 执行驾车路径规划
                    driving.search(
                        new AMap.LngLat(startLon, startLat),
                        new AMap.LngLat(endLon, endLat),
                        function(status, result) {
                            console.log('📡 高德驾车路径规划回调 - 状态:', status);
                            console.log('📡 回调结果:', result);

                            if (status === 'complete') {
                                console.log('✅ 驾车路径规划成功！');

                                // 获取路径信息
                                if (result.routes && result.routes.length > 0) {
                                    var route = result.routes[0];
                                    var distance = (route.distance / 1000).toFixed(1); // 转换为公里
                                    var duration = Math.round(route.time / 60); // 转换为分钟

                                    console.log('📏 路径距离:', distance + 'km');
                                    console.log('⏱️ 预计时间:', duration + '分钟');

                                    // 显示导航信息
                                    showNavigationInfo(distance, duration);

                                    // 设置导航状态
                                    isNavigating = true;

                                    alert('✅ 导航路线规划成功！\n距离: ' + distance + 'km\n预计时间: ' + duration + '分钟');
                                } else {
                                    console.log('⚠️ 没有找到路径');
                                    alert('⚠️ 没有找到合适的路径');
                                }

                            } else if (status === 'error') {
                                console.error('❌ 驾车路径规划失败:', result);
                                alert('❌ 路径规划失败: ' + (result.info || '未知错误'));
                            } else {
                                console.log('⚠️ 路径规划状态:', status, result);
                                alert('⚠️ 路径规划状态: ' + status);
                            }
                        }
                    );

                } else {
                    console.error('❌ AMap对象或map对象不存在');
                    console.log('AMap类型:', typeof AMap);
                    console.log('map类型:', typeof map);
                    alert('❌ 地图API未正确加载，无法进行导航');
                }

            } catch (error) {
                console.error('❌ 驾车导航启动失败:', error);
                alert('❌ 导航启动失败: ' + error.message);
            }
        }

        // 上传导航数据到ThingSpeak
        function uploadNavigationData(lat, lon, alt, navigationCmd) {
            console.log('📤 上传导航数据到ThingSpeak...');

            // 构建ThingSpeak URL
            var thingSpeakURL = 'https://api.thingspeak.com/update?api_key=LU22ZUP4ZTFK4IY9';
            thingSpeakURL += '&field1=' + lat.toFixed(6);
            thingSpeakURL += '&field2=' + lon.toFixed(6);

            // Field3格式：高度|导航指令
            var field3Value = alt.toFixed(1) + '|' + navigationCmd;
            thingSpeakURL += '&field3=' + encodeURIComponent(field3Value);

            console.log('🔗 上传URL:', thingSpeakURL);

            // 发送数据
            fetch(thingSpeakURL)
                .then(response => response.text())
                .then(result => {
                    if (result && result !== '0') {
                        console.log('✅ 导航数据上传成功，Entry ID:', result);
                    } else {
                        console.error('❌ 导航数据上传失败');
                    }
                })
                .catch(error => {
                    console.error('❌ 上传失败:', error);
                });
        }

        // 清除导航路线
        function clearNavigationRoute() {
            console.log('🧹 清除导航路线');

            // 清除高德地图的路径规划结果
            if (driving) {
                driving.clear();
            }

            // 清除自定义的路径和标记
            if (navigationRoute) {
                map.remove(navigationRoute);
                navigationRoute = null;
            }

            if (startMarker) {
                map.remove(startMarker);
                startMarker = null;
            }

            if (endMarker) {
                map.remove(endMarker);
                endMarker = null;
            }

            // 清除导航信息显示
            var navInfo = document.getElementById('nav-info');
            if (navInfo) {
                navInfo.remove();
            }

            isNavigating = false;
            console.log('✅ 导航路线已清除');
        }

        // 模拟串口输入（用于测试）
        function simulateSerialInput() {
            console.log('🧪 模拟串口输入: wanda');
            handleSerialCommand('wanda');
        }
    </script>
</body>
</html>
