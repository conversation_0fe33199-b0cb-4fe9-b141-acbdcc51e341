# 串口1导航命令使用说明

## 🎯 系统配置
- **串口1 (USART1)**: 115200波特率，用于调试输出和命令输入
- **串口2 (USART2)**: ESP-01 WiFi模块通信
- **串口3 (USART3)**: GPS模块数据接收

## 📝 导航命令列表

### 直接导航命令（通过串口1输入）
```
wangda          # 导航到酃湖万达广场
gaotie          # 导航到衡阳东高铁站  
daxue           # 导航到衡阳师范学院
yiyuan          # 导航到南华大学附属第一医院
gongyuan        # 导航到石鼓公园
shangchang      # 导航到华新开发区
jichang         # 导航到衡阳南岳机场
xuexiao         # 导航到衡阳师范学院东校区
zhongxin        # 导航到衡阳市中心
huochezhan      # 导航到衡阳火车站
```

### 系统管理命令
```
nav_help        # 显示导航帮助
nav_list        # 显示所有可用目的地
nav_status      # 显示当前导航状态
nav_stop        # 停止当前导航
```

### 其他系统命令
```
gps_status      # 查看GPS状态
esp_status      # 查看ESP01状态
get_location    # 获取当前位置
send_location   # 手动上传位置
```

## 🚀 使用示例

### 1. 导航到万达广场
**输入：** `wangda`

**输出：**
```
开始导航到: 酃湖万达广场
总距离: 1250米
向东北方向行驶 1250米
```

### 2. 查看导航状态
**输入：** `nav_status`

**输出：**
```
导航状态:
状态: 导航中
目的地: 酃湖万达广场
剩余距离: 856米
```

### 3. 查看可用目的地
**输入：** `nav_list`

**输出：**
```
可用目的地:
wangda - 酃湖万达广场
gaotie - 衡阳东高铁站
daxue - 衡阳师范学院
yiyuan - 南华大学附属第一医院
gongyuan - 石鼓公园
shangchang - 华新开发区
jichang - 衡阳南岳机场
xuexiao - 衡阳师范学院东校区
zhongxin - 衡阳市中心
huochezhan - 衡阳火车站
```

### 4. 停止导航
**输入：** `nav_stop`

**输出：**
```
导航已停止
```

## ⚠️ 注意事项

1. **GPS信号要求**：导航前确保GPS有有效信号
2. **命令格式**：命令区分大小写，请准确输入
3. **串口设置**：确保串口助手设置为115200波特率
4. **回车符**：命令后需要发送回车符(\r\n)

## 🔧 故障排除

### 命令无响应
- 检查串口连接和波特率设置
- 确认命令拼写正确
- 确保发送了回车符

### GPS信号无效
- 将设备移到室外开阔地带
- 等待GPS模块获取卫星信号
- 使用`gps_status`检查GPS状态

### 导航无法开始
- 确认GPS信号有效
- 检查目的地名称是否正确
- 使用`nav_list`查看可用目的地

---

**现在您可以通过串口1直接输入`wangda`开始导航了！**
